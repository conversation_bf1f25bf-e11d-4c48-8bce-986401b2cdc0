# 🎯 GodlySharing Channel Analyzer

## 🌙 Для ночного тестирования Икой

Система анализа канала @GodlySharing для создания лучшего контента на основе успешных паттернов.

## 🚀 Быстрый запуск

### 1. Автоматический запуск
```bash
./start_analyzer.sh
```

### 2. Ручной запуск (если нужно)
```bash
# Установка зависимостей
pip3 install -r requirements_analyzer.txt
pip3 install fastapi uvicorn

# Запуск API
python3 analyzer_api.py

# Открыть веб-интерфейс
open analyzer_dashboard.html
```

## 📊 Что делает система

### 🔍 Анализ канала
1. **Получает список видео** через YouTube API
2. **Собирает статистику** (просмотры, лайки, длительность)
3. **Создает транскрипции** с помощью Whisper AI
4. **Анализирует паттерны** успешного контента
5. **Генерирует отчет** с рекомендациями

### 📈 Что анализируется
- **Средние просмотры** и вовлеченность
- **Популярные ключевые слова** в видео
- **Оптимальная длительность** контента
- **Темы и паттерны** успешных видео
- **Топ-10 самых успешных** видео

## 🎮 Использование

### Веб-интерфейс
1. Откройте `analyzer_dashboard.html` в браузере
2. Нажмите **"🚀 Запустить анализ"**
3. Следите за прогрессом в реальном времени
4. Получите результаты и рекомендации

### API эндпоинты
- `POST /analyze-channel` - Запуск анализа
- `GET /analysis-status` - Статус выполнения
- `GET /analysis-results` - Результаты анализа
- `GET /analysis-report` - Читаемый отчет

## 📁 Структура файлов

```
VideoLingo-MLX/
├── youtube_analyzer.py      # Основной анализатор
├── analyzer_api.py          # FastAPI сервер
├── analyzer_dashboard.html  # Веб-интерфейс
├── test_analyzer.py         # Тестирование
├── start_analyzer.sh        # Автозапуск
├── requirements_analyzer.txt # Зависимости
└── analysis_data/           # Результаты
    ├── transcripts/         # Транскрипции видео
    ├── audio/              # Аудио файлы
    ├── analysis_report.json # JSON отчет
    └── ANALYSIS_REPORT.md   # Читаемый отчет
```

## 🔧 Настройки

### Параметры анализа
```python
{
    "max_videos": 20,           # Максимум видео для анализа
    "include_transcripts": true, # Создавать транскрипции
    "notify_telegram": true     # Уведомления в Telegram
}
```

### YouTube API
- **Ключ**: `AIzaSyAeI-U4Et6xrWx4IX5Hol14eZyJAk0UGfU`
- **Канал**: `@GodlySharing`
- **Лимит**: 10,000 запросов/день

### Telegram уведомления
- **Бот**: `**********************************************`
- **Чат**: `-1001957777708`

## ⚡ Быстрое тестирование

### Проверка системы
```bash
python3 test_analyzer.py
# Выберите: 1 - Базовое тестирование
```

### Быстрый анализ (3 видео)
```bash
python3 test_analyzer.py
# Выберите: 2 - Быстрый анализ
```

### Полный анализ (20 видео)
```bash
python3 test_analyzer.py
# Выберите: 3 - Полный анализ
```

## 📊 Пример результата

```
📊 Анализ канала @GodlySharing

🎯 Общая статистика
- Всего видео: 20
- Средние просмотры: 45,230
- Средние лайки: 1,250
- Средняя длительность: 8 минут

🔥 Топ-3 успешных видео
1. "Как заработать в интернете" - 120K просмотров
2. "Секреты успеха" - 95K просмотров  
3. "Мотивация каждый день" - 87K просмотров

🔑 Популярные темы
- заработок, успех, мотивация, деньги, бизнес

💡 Рекомендации
- Оптимальная длительность: 6-10 минут
- Популярные темы: заработок + мотивация
- Лучшее время: утро (больше вовлеченности)
```

## 🔗 Интеграция с N8N

Создан workflow для автоматического анализа:
- **ID**: `y7eA0ZCL5WJMVp6C`
- **Название**: "GodlySharing - Simple Analyzer"
- **Расписание**: Каждый понедельник в 9:00

## 🚨 Troubleshooting

### Ошибка YouTube API
```bash
# Проверьте ключ API
curl "https://www.googleapis.com/youtube/v3/search?part=snippet&q=@GodlySharing&type=channel&key=YOUR_KEY"
```

### Ошибка Whisper
```bash
# Переустановите Whisper
pip3 uninstall openai-whisper
pip3 install openai-whisper
```

### Ошибка ffmpeg
```bash
# macOS
brew install ffmpeg

# Linux
sudo apt install ffmpeg
```

## 🎯 Следующие шаги

После анализа используйте результаты для:
1. **Создания нового контента** на основе успешных паттернов
2. **Оптимизации длительности** видео
3. **Выбора популярных тем** и ключевых слов
4. **Улучшения заголовков** и описаний

---

**🌙 Удачного ночного тестирования, Ика!**

Система готова к работе. Все настроено для анализа @GodlySharing и создания данных для улучшения контента.
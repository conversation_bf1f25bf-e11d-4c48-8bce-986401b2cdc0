# 🚀 Быстрый запуск GodlySharing <PERSON><PERSON><PERSON> с HTTPS

## Мгновенный запуск (1 минута)

```bash
# 1. Переходим в папку проекта
cd /Users/<USER>/Projects/MCP-SuperAssistant/VideoLingo-MLX

# 2. Запускаем через Tailscale (автоматический HTTPS)
./start_tailscale_serve.sh
```

**Готово!** Откройте: https://ikas-macbook-pro.tailf48d3.ts.net/dashboard

## Что происходит автоматически

✅ **Проверка Tailscale** - подключение к сети  
✅ **Установка зависимостей** - Python пакеты  
✅ **Запуск сервера** - FastAPI на localhost:8080  
✅ **Настройка HTTPS** - Tailscale Serve на порт 443  
✅ **Валидные SSL** - автоматические сертификаты  

## Ваши адреса доступа

🌐 **Основной:** https://ikas-macbook-pro.tailf48d3.ts.net  
📊 **Дашборд:** https://ikas-macbook-pro.tailf48d3.ts.net/dashboard  
📖 **API:** https://ikas-macbook-pro.tailf48d3.ts.net/docs  
📱 **IP:** https://************  

## Функции веб-интерфейса

- 🎬 **Анализ канала @GodlySharing**
- 📊 **Настройка параметров** (10-100 видео)
- 🎤 **AI транскрипция** содержания
- 📈 **Анализ паттернов** успешного контента
- 📱 **Telegram уведомления**
- 📥 **Скачивание отчетов**

## Остановка

```bash
# Нажмите Ctrl+C в терминале
# Все сервисы остановятся автоматически
```

## Устранение проблем

### Tailscale не подключен
```bash
tailscale up
```

### Зависимости отсутствуют
```bash
pip3 install fastapi uvicorn google-api-python-client openai-whisper yt-dlp
```

### Порт занят
```bash
sudo lsof -i :443
sudo kill -9 <PID>
```

---

**🎯 Один скрипт. Полная автоматизация. Безопасный HTTPS.** 
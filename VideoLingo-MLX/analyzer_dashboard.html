<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GodlySharing Channel Analyzer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.2em;
        }
        
        .controls {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #FF9800, #F57C00);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .status-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #4CAF50;
        }
        
        .status-running {
            border-left-color: #FF9800;
            background: #fff3e0;
        }
        
        .status-error {
            border-left-color: #f44336;
            background: #ffebee;
        }
        
        .status-idle {
            border-left-color: #9E9E9E;
            background: #f5f5f5;
        }
        
        .status-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .status-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .status-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status-item-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }
        
        .status-item-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        
        .results-panel {
            background: #e8f5e8;
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            display: none;
        }
        
        .results-panel.show {
            display: block;
        }
        
        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .result-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .result-card h3 {
            color: #4CAF50;
            margin-bottom: 10px;
        }
        
        .result-number {
            font-size: 2em;
            font-weight: bold;
            color: #333;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4CAF50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .log-panel {
            background: #263238;
            color: #4CAF50;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 GodlySharing Channel Analyzer</h1>
            <p>Анализ канала @GodlySharing для создания лучшего контента</p>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="startAnalysis()">
                🚀 Запустить анализ
            </button>
            <button class="btn btn-secondary" onclick="checkStatus()">
                📊 Проверить статус
            </button>
            <button class="btn btn-warning" onclick="viewResults()">
                📋 Посмотреть результаты
            </button>
        </div>
        
        <div id="statusPanel" class="status-panel status-idle">
            <div class="status-title">
                <span id="statusIcon">⏸️</span>
                <span id="statusText">Система готова к работе</span>
            </div>
            
            <div id="progressContainer" style="display: none;">
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill" style="width: 0%"></div>
                </div>
                <div id="currentTask">Подготовка...</div>
            </div>
            
            <div id="statusDetails" class="status-details" style="display: none;">
                <div class="status-item">
                    <div class="status-item-label">Начало анализа</div>
                    <div id="startTime" class="status-item-value">-</div>
                </div>
                <div class="status-item">
                    <div class="status-item-label">Видео обработано</div>
                    <div id="videosProcessed" class="status-item-value">0</div>
                </div>
                <div class="status-item">
                    <div class="status-item-label">Текущее видео</div>
                    <div id="currentVideo" class="status-item-value">-</div>
                </div>
            </div>
        </div>
        
        <div id="resultsPanel" class="results-panel">
            <h2>📊 Результаты анализа</h2>
            <div id="resultsGrid" class="results-grid">
                <!-- Результаты будут добавлены динамически -->
            </div>
        </div>
        
        <div id="logPanel" class="log-panel" style="display: none;">
            <div id="logContent"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'https://localhost:8444';
        let statusCheckInterval;
        
        async function startAnalysis() {
            try {
                const response = await fetch(`${API_BASE}/analyze-channel`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        max_videos: 20,
                        include_transcripts: true,
                        notify_telegram: true
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    updateStatus('running', '🔄 Анализ запущен', data.message);
                    startStatusChecking();
                } else {
                    updateStatus('error', '❌ Ошибка', data.detail);
                }
            } catch (error) {
                updateStatus('error', '❌ Ошибка подключения', error.message);
            }
        }
        
        async function checkStatus() {
            try {
                const response = await fetch(`${API_BASE}/analysis-status`);
                const data = await response.json();
                
                updateStatusFromData(data);
            } catch (error) {
                updateStatus('error', '❌ Ошибка', 'Не удается получить статус');
            }
        }
        
        async function viewResults() {
            try {
                const response = await fetch(`${API_BASE}/analysis-results`);
                const data = await response.json();
                
                if (response.ok) {
                    showResults(data);
                } else {
                    alert('Результаты анализа пока недоступны');
                }
            } catch (error) {
                alert('Ошибка получения результатов');
            }
        }
        
        function updateStatus(status, title, message) {
            const panel = document.getElementById('statusPanel');
            const icon = document.getElementById('statusIcon');
            const text = document.getElementById('statusText');
            
            // Удаляем все классы статуса
            panel.className = 'status-panel';
            
            switch (status) {
                case 'running':
                    panel.classList.add('status-running');
                    icon.textContent = '🔄';
                    break;
                case 'completed':
                    panel.classList.add('status-completed');
                    icon.textContent = '✅';
                    break;
                case 'error':
                    panel.classList.add('status-error');
                    icon.textContent = '❌';
                    break;
                default:
                    panel.classList.add('status-idle');
                    icon.textContent = '⏸️';
            }
            
            text.textContent = title;
        }
        
        function updateStatusFromData(data) {
            updateStatus(data.status, getStatusTitle(data.status), '');
            
            if (data.status === 'running') {
                document.getElementById('progressContainer').style.display = 'block';
                document.getElementById('statusDetails').style.display = 'grid';
                
                document.getElementById('startTime').textContent = new Date(data.started_at).toLocaleString();
                document.getElementById('videosProcessed').textContent = data.videos_processed;
                document.getElementById('currentVideo').textContent = data.current_video;
                
                // Примерный прогресс (если известно общее количество)
                const progress = Math.min(data.videos_processed * 5, 100); // 20 видео = 100%
                document.getElementById('progressFill').style.width = `${progress}%`;
                document.getElementById('currentTask').textContent = data.current_video;
            } else {
                document.getElementById('progressContainer').style.display = 'none';
                if (data.status !== 'completed') {
                    document.getElementById('statusDetails').style.display = 'none';
                }
            }
            
            if (data.status === 'completed') {
                stopStatusChecking();
                checkStatus(); // Получаем финальные результаты
            }
        }
        
        function getStatusTitle(status) {
            switch (status) {
                case 'running': return '🔄 Анализ выполняется';
                case 'completed': return '✅ Анализ завершен';
                case 'error': return '❌ Произошла ошибка';
                default: return '⏸️ Система готова к работе';
            }
        }
        
        function startStatusChecking() {
            statusCheckInterval = setInterval(checkStatus, 5000); // Каждые 5 секунд
        }
        
        function stopStatusChecking() {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
                statusCheckInterval = null;
            }
        }
        
        function showResults(data) {
            const panel = document.getElementById('resultsPanel');
            const grid = document.getElementById('resultsGrid');
            
            const summary = data.summary;
            
            grid.innerHTML = `
                <div class="result-card">
                    <h3>📺 Всего видео</h3>
                    <div class="result-number">${summary.total_videos}</div>
                </div>
                <div class="result-card">
                    <h3>👀 Средние просмотры</h3>
                    <div class="result-number">${summary.avg_views.toLocaleString()}</div>
                </div>
                <div class="result-card">
                    <h3>👍 Средние лайки</h3>
                    <div class="result-number">${summary.avg_likes.toLocaleString()}</div>
                </div>
                <div class="result-card">
                    <h3>⏱️ Средняя длительность</h3>
                    <div class="result-number">${Math.round(summary.avg_duration / 60)} мин</div>
                </div>
            `;
            
            panel.classList.add('show');
        }
        
        // Проверяем статус при загрузке страницы
        window.onload = function() {
            checkStatus();
        };
    </script>
</body>
</html>
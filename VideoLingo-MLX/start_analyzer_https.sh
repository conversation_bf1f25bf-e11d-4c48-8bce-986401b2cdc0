#!/bin/bash

# Скрипт запуска GodlySharing Channel Analyzer с HTTPS через Tailscale
# Автор: Ika & Claude
# Версия: 1.0.0

echo "🔐 Запуск GodlySharing Channel Analyzer с HTTPS через Tailscale"
echo "=================================================="

# Проверяем, что мы в правильной директории
if [ ! -f "youtube_analyzer.py" ]; then
    echo "❌ Ошибка: Файл youtube_analyzer.py не найден"
    echo "💡 Убедитесь, что вы находитесь в директории VideoLingo-MLX"
    exit 1
fi

# Проверяем статус Tailscale
echo "🔍 Проверка статуса Tailscale..."
if ! command -v tailscale &> /dev/null; then
    echo "❌ Tailscale не установлен"
    echo "💡 Установите Tailscale: https://tailscale.com/download"
    exit 1
fi

# Проверяем подключение к Tailscale
TAILSCALE_STATUS=$(tailscale status --json 2>/dev/null)
if [ $? -ne 0 ]; then
    echo "❌ Tailscale не подключен"
    echo "💡 Выполните: tailscale up"
    exit 1
fi

# Получаем информацию о текущем устройстве
TAILSCALE_IP=$(tailscale ip -4)
TAILSCALE_HOSTNAME=$(tailscale status --json | python3 -c "
import json, sys
data = json.load(sys.stdin)
for peer in data['Peer'].values():
    if peer['TailscaleIPs'][0] == '$TAILSCALE_IP':
        print(peer['DNSName'].rstrip('.'))
        break
" 2>/dev/null)

echo "✅ Tailscale подключен:"
echo "   IP: $TAILSCALE_IP"
echo "   Домен: $TAILSCALE_HOSTNAME"

# Проверяем зависимости Python
echo "📦 Проверка зависимостей Python..."
python3 -c "
import sys
required = ['fastapi', 'uvicorn', 'googleapiclient', 'whisper', 'yt_dlp']
missing = []

for package in required:
    try:
        __import__(package)
    except ImportError:
        missing.append(package)

if missing:
    print('❌ Отсутствуют пакеты:', ', '.join(missing))
    print('💡 Установите: pip3 install', ' '.join(missing))
    sys.exit(1)
else:
    print('✅ Все зависимости установлены')
"

if [ $? -ne 0 ]; then
    exit 1
fi

# Проверяем конфигурацию YouTube API
if [ ! -f "config.json" ]; then
    echo "❌ Файл config.json не найден"
    echo "💡 Создайте конфигурацию с YouTube API ключом"
    exit 1
fi

# Проверяем YouTube API ключ
YOUTUBE_API_KEY=$(python3 -c "
import json
try:
    with open('config.json', 'r') as f:
        config = json.load(f)
    print(config.get('youtube_api_key', ''))
except:
    print('')
")

if [ -z "$YOUTUBE_API_KEY" ]; then
    echo "❌ YouTube API ключ не найден в config.json"
    echo "💡 Добавьте 'youtube_api_key' в конфигурацию"
    exit 1
fi

echo "✅ YouTube API ключ настроен"

# Создаем папку для результатов
mkdir -p analysis_data

# Проверяем доступность порта 443 (может потребоваться sudo)
echo "🔌 Проверка порта 443..."
if lsof -i :443 &> /dev/null; then
    echo "⚠️  Порт 443 занят. Попытка освободить..."
    sudo lsof -ti :443 | xargs sudo kill -9 2>/dev/null
    sleep 2
fi

# Информация о доступе
echo ""
echo "🌐 Сервер будет доступен по адресам:"
echo "   https://$TAILSCALE_IP"
echo "   https://$TAILSCALE_HOSTNAME"
echo ""
echo "📊 Веб-интерфейс:"
echo "   https://$TAILSCALE_HOSTNAME/dashboard"
echo ""
echo "🔗 API документация:"
echo "   https://$TAILSCALE_HOSTNAME/docs"
echo ""

# Запрашиваем подтверждение
read -p "🚀 Запустить HTTPS сервер? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Запуск отменен"
    exit 0
fi

echo "🔥 Запуск HTTPS сервера..."
echo "💡 Для остановки нажмите Ctrl+C"
echo ""

# Запускаем HTTPS сервер (может потребоваться sudo для порта 443)
if [ "$EUID" -ne 0 ]; then
    echo "🔐 Для использования порта 443 требуются права администратора"
    echo "💡 Введите пароль для sudo..."
    sudo python3 analyzer_api_https.py
else
    python3 analyzer_api_https.py
fi 
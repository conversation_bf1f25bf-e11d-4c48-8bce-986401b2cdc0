#!/usr/bin/env python3
"""
Простой HTTP сервер с CORS для analyzer_dashboard.html
"""

import http.server
import socketserver
import os
import json
from datetime import datetime

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()
    
    def do_GET(self):
        # API эндпоинты
        if self.path == '/analysis-status':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            status = {
                "status": "idle",
                "started_at": None,
                "videos_processed": 0,
                "current_video": "Система готова к работе",
                "estimated_completion": ""
            }
            
            self.wfile.write(json.dumps(status).encode())
            return
            
        elif self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            info = {
                "service": "GodlySharing Channel Analyzer",
                "version": "1.0.0",
                "status": "active",
                "protocol": "HTTP",
                "endpoints": {
                    "analyze": "/analyze-channel",
                    "status": "/analysis-status",
                    "results": "/analysis-results"
                }
            }
            
            self.wfile.write(json.dumps(info).encode())
            return
        
        # Обычные файлы
        super().do_GET()
    
    def do_POST(self):
        if self.path == '/analyze-channel':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            response = {
                "message": "Анализ канала @GodlySharing запущен (демо режим)",
                "status": "running",
                "estimated_time": "5 минут",
                "check_status": "/analysis-status"
            }
            
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            return
        
        super().do_POST()

if __name__ == "__main__":
    PORT = 8080
    
    # Переходим в рабочую директорию
    os.chdir('/Users/<USER>/Projects/MCP-SuperAssistant/VideoLingo-MLX')
    
    with socketserver.TCPServer(("", PORT), CORSHTTPRequestHandler) as httpd:
        print(f"🌐 HTTP сервер запущен на http://localhost:{PORT}")
        print(f"📄 Откройте: http://localhost:{PORT}/analyzer_dashboard.html")
        print(f"📊 API: http://localhost:{PORT}/")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n⏹️ Сервер остановлен")
            httpd.shutdown()
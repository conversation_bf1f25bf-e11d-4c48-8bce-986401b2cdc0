#!/bin/bash

# Скрипт запуска GodlySharing Analyzer через Tailscale Serve
# Автоматически настраивает HTTPS через Tailscale

echo "🔐 Запуск GodlySharing Analyzer через Tailscale Serve"
echo "=================================================="

# Проверяем Tailscale
if ! command -v tailscale &> /dev/null; then
    echo "❌ Tailscale не установлен"
    echo "💡 Установите: https://tailscale.com/download"
    exit 1
fi

# Проверяем подключение
if ! tailscale status &> /dev/null; then
    echo "❌ Tailscale не подключен"
    echo "💡 Выполните: tailscale up"
    exit 1
fi

# Получаем информацию
TAILSCALE_IP=$(tailscale ip -4)
TAILSCALE_HOSTNAME="ikas-macbook-pro.tailf48d3.ts.net"

echo "✅ Tailscale подключен:"
echo "   IP: $TAILSCALE_IP"
echo "   Домен: $TAILSCALE_HOSTNAME"

# Проверяем зависимости
echo "📦 Проверка зависимостей..."
python3 -c "
try:
    import fastapi, uvicorn, googleapiclient, whisper, yt_dlp
    print('✅ Все зависимости установлены')
except ImportError as e:
    print('❌ Отсутствует:', str(e))
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "💡 Установите: pip3 install fastapi uvicorn google-api-python-client openai-whisper yt-dlp"
    exit 1
fi

# Останавливаем существующие процессы
echo "🧹 Остановка существующих процессов..."
pkill -f "analyzer_api_tailscale.py" 2>/dev/null
pkill -f "tailscale serve" 2>/dev/null
sleep 2

# Запускаем Python сервер в фоне
echo "🚀 Запуск Python сервера..."
python3 analyzer_api_tailscale.py &
SERVER_PID=$!

# Ждем запуска сервера
sleep 3

# Проверяем, что сервер запустился
if ! curl -s http://localhost:8080 > /dev/null; then
    echo "❌ Ошибка запуска Python сервера"
    kill $SERVER_PID 2>/dev/null
    exit 1
fi

echo "✅ Python сервер запущен (PID: $SERVER_PID)"

# Настраиваем Tailscale Serve для HTTPS
echo "🔗 Настройка Tailscale Serve для HTTPS..."
tailscale serve https:443 http://localhost:8080 &
SERVE_PID=$!

sleep 2

echo ""
echo "🎉 Система запущена успешно!"
echo ""
echo "🌐 Доступ к анализатору:"
echo "   https://$TAILSCALE_HOSTNAME"
echo "   https://$TAILSCALE_HOSTNAME/dashboard"
echo ""
echo "📖 API документация:"
echo "   https://$TAILSCALE_HOSTNAME/docs"
echo ""
echo "🔐 Безопасное HTTPS соединение через Tailscale активно"
echo ""
echo "💡 Для остановки нажмите Ctrl+C"

# Функция для корректной остановки
cleanup() {
    echo ""
    echo "🛑 Остановка сервисов..."
    kill $SERVER_PID 2>/dev/null
    kill $SERVE_PID 2>/dev/null
    tailscale serve reset 2>/dev/null
    echo "✅ Все сервисы остановлены"
    exit 0
}

# Обработчик сигналов
trap cleanup SIGINT SIGTERM

# Мониторинг процессов
while true; do
    if ! kill -0 $SERVER_PID 2>/dev/null; then
        echo "❌ Python сервер остановился"
        cleanup
    fi
    
    if ! kill -0 $SERVE_PID 2>/dev/null; then
        echo "❌ Tailscale Serve остановился"
        cleanup
    fi
    
    sleep 5
done 
#!/usr/bin/env python3
"""
GodlySharing Channel Analyzer для Tailscale
Использует Tailscale Serve для автоматического HTTPS
"""

from fastapi import FastAPI, BackgroundTasks, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from typing import Optional
import json
import asyncio
from datetime import datetime
from pathlib import Path
import subprocess
import sys
import uvicorn

app = FastAPI(
    title="GodlySharing Channel Analyzer (Tailscale)",
    version="1.0.0",
    description="Анализ канала @GodlySharing через безопасное соединение Tailscale"
)

# Настройка CORS для Tailscale
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "https://ikas-macbook-pro.tailf48d3.ts.net",
        "https://************",
        "http://localhost:8080",
        "https://localhost:8080"
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

class AnalysisRequest(BaseModel):
    max_videos: int = 20
    include_transcripts: bool = True
    notify_telegram: bool = True
    download_path: Optional[str] = "~/Downloads/GodlySharing_Analysis"

class AnalysisStatus(BaseModel):
    status: str
    started_at: Optional[str] = None
    videos_downloaded: int = 0  # Количество загруженных метаданных
    videos_processed: int = 0   # Количество транскрибированных
    current_video: str = ""
    estimated_completion: str = ""
    # Промежуточные результаты для динамического обновления
    intermediate_stats: Optional[dict] = None

# Глобальное состояние анализа
analysis_state = {
    "status": "idle",
    "started_at": None,
    "videos_downloaded": 0,  # Количество загруженных метаданных
    "videos_processed": 0,   # Количество транскрибированных
    "current_video": "",
    "results": None,
    "error": None,
    "intermediate_stats": None  # Промежуточные результаты
}

@app.get("/")
async def root():
    return {
        "service": "GodlySharing Channel Analyzer",
        "version": "1.0.0",
        "status": "active",
        "tailscale": {
            "enabled": True,
            "domain": "ikas-macbook-pro.tailf48d3.ts.net",
            "ip": "************",
            "secure": True
        },
        "endpoints": {
            "dashboard": "/dashboard",
            "analyze": "/analyze-channel",
            "status": "/analysis-status",
            "results": "/analysis-results",
            "report": "/analysis-report"
        }
    }

@app.get("/dashboard", response_class=HTMLResponse)
async def get_dashboard():
    """Веб-интерфейс анализатора"""
    
    # Создаем встроенный dashboard для Tailscale
    html_content = """
    <!DOCTYPE html>
    <html lang="ru">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>GodlySharing Channel Analyzer</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: white;
            }
            
            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }
            
            .header {
                text-align: center;
                margin-bottom: 40px;
            }
            
            .header h1 {
                font-size: 3rem;
                margin-bottom: 10px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            
            .header p {
                font-size: 1.2rem;
                opacity: 0.9;
            }
            
            .tailscale-info {
                background: rgba(255,255,255,0.1);
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 30px;
                backdrop-filter: blur(10px);
            }
            
            .control-panel {
                background: rgba(255,255,255,0.1);
                border-radius: 15px;
                padding: 30px;
                margin-bottom: 30px;
                backdrop-filter: blur(10px);
            }
            
            .form-group {
                margin-bottom: 20px;
            }
            
            label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
            }
            
            input, select {
                width: 100%;
                padding: 12px;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                background: rgba(255,255,255,0.9);
                color: #333;
            }
            
            button {
                background: linear-gradient(45deg, #ff6b6b, #ee5a24);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 18px;
                font-weight: bold;
                cursor: pointer;
                transition: transform 0.2s;
                width: 100%;
            }
            
            button:hover {
                transform: translateY(-2px);
            }
            
            button:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
            }
            
            .status-panel {
                background: rgba(255,255,255,0.1);
                border-radius: 15px;
                padding: 20px;
                backdrop-filter: blur(10px);
            }
            
            .status-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
            }
            
            .logs-panel {
                background: rgba(0,0,0,0.3);
                border-radius: 15px;
                padding: 20px;
                margin-top: 20px;
                backdrop-filter: blur(10px);
                max-height: 400px;
                overflow-y: auto;
            }
            
            .log-entry {
                font-family: 'Monaco', 'Consolas', monospace;
                font-size: 14px;
                margin-bottom: 5px;
                padding: 5px;
                border-left: 3px solid #4CAF50;
                padding-left: 10px;
            }
            
            .log-error {
                border-left-color: #f44336;
                color: #ffcdd2;
            }
            
            .log-info {
                border-left-color: #2196F3;
                color: #bbdefb;
            }
            
            .log-success {
                border-left-color: #4CAF50;
                color: #c8e6c9;
                padding: 10px;
                background: rgba(255,255,255,0.05);
                border-radius: 8px;
            }
            
            .progress-bar {
                width: 100%;
                height: 20px;
                background: rgba(255,255,255,0.2);
                border-radius: 10px;
                overflow: hidden;
                margin: 10px 0;
            }
            
            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #00d2ff, #3a7bd5);
                width: 0%;
                transition: width 0.3s ease;
            }
            
            .results-panel {
                background: rgba(255,255,255,0.1);
                border-radius: 15px;
                padding: 20px;
                margin-top: 20px;
                backdrop-filter: blur(10px);
                display: none;
            }
            
            .emoji {
                font-size: 1.5em;
                margin-right: 10px;
            }
            
            @media (max-width: 768px) {
                .header h1 {
                    font-size: 2rem;
                }
                
                .container {
                    padding: 10px;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎬 GodlySharing Analyzer</h1>
                <p>Анализ YouTube канала через защищенное соединение Tailscale</p>
            </div>
            
            <div class="tailscale-info">
                <h3><span class="emoji">🔐</span>Безопасное соединение</h3>
                <div class="status-item">
                    <span>Домен:</span>
                    <span>ikas-macbook-pro.tailf48d3.ts.net</span>
                </div>
                <div class="status-item">
                    <span>IP:</span>
                    <span>************</span>
                </div>
                <div class="status-item">
                    <span>Статус:</span>
                    <span style="color: #00ff88;">✅ Подключено</span>
                </div>
            </div>
            
            <div class="control-panel">
                <h3><span class="emoji">⚙️</span>Настройки анализа</h3>
                
                <div class="form-group">
                    <label for="max_videos">Максимум видео для анализа:</label>
                    <select id="max_videos">
                        <option value="10">10 видео (быстро)</option>
                        <option value="20" selected>20 видео (рекомендуется)</option>
                        <option value="50">50 видео (подробно)</option>
                        <option value="100">100 видео (полный анализ)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="include_transcripts" checked>
                        Создавать транскрипции (AI анализ содержания)
                    </label>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="notify_telegram" checked>
                        Отправить уведомление в Telegram
                    </label>
                </div>
                
                <div class="form-group">
                    <label for="download_path">📁 Папка для сохранения клипов:</label>
                    <div style="display: flex; gap: 10px; margin-top: 5px;">
                        <input type="text" id="download_path" placeholder="~/Downloads/GodlySharing_Analysis" readonly style="flex: 1; background: rgba(255,255,255,0.7);">
                        <button type="button" onclick="selectDownloadPath()" style="width: auto; padding: 8px 16px; background: linear-gradient(45deg, #4CAF50, #45a049);">
                            📂 Выбрать
                        </button>
                    </div>
                    <small style="opacity: 0.8; font-size: 12px;">Клипы будут сохранены в выбранной папке</small>
                </div>
                
                <button onclick="startAnalysis()" id="startBtn">
                    🚀 Запустить анализ
                </button>
            </div>
            
            <div class="status-panel">
                <h3><span class="emoji">📊</span>Статус анализа</h3>
                <div class="status-item">
                    <span>Состояние:</span>
                    <span id="status">Ожидание</span>
                </div>
                <div class="status-item">
                    <span>Текущее действие:</span>
                    <span id="current_video">-</span>
                </div>
                
                <!-- Прогресс загрузки метаданных -->
                <div class="status-item">
                    <span>📥 Загрузка данных:</span>
                    <span id="download_progress">0/0</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="download_progress_bar" style="background: #4CAF50;"></div>
                </div>
                
                <!-- Прогресс транскрипции -->
                <div class="status-item">
                    <span>🎙️ Транскрипция:</span>
                    <span id="transcription_progress">0/0</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="transcription_progress_bar" style="background: #FF9800;"></div>
                </div>
            </div>
            
            <div class="results-panel" id="resultsPanel">
                <h3><span class="emoji">📈</span>Результаты анализа</h3>
                <div id="results"></div>
                <button onclick="downloadReport()" style="margin-top: 20px;">
                    📥 Скачать отчет
                </button>
            </div>
            
            <!-- Окно логов -->
            <div class="logs-panel">
                <h3>📋 Логи выполнения</h3>
                <div id="logs-container">
                    <div class="log-entry log-info">Система готова к анализу</div>
                </div>
            </div>
        </div>
        
        <script>
            const API_BASE = window.location.origin;
            let analysisInterval;
            let lastLogMessage = '';
            
            function addLog(message, type = 'info') {
                // Избегаем дублирования одинаковых сообщений
                if (message === lastLogMessage) return;
                lastLogMessage = message;
                
                const logsContainer = document.getElementById('logs-container');
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry log-${type}`;
                
                const timestamp = new Date().toLocaleTimeString();
                logEntry.textContent = `[${timestamp}] ${message}`;
                
                logsContainer.appendChild(logEntry);
                
                // Автоскролл к последнему сообщению
                logsContainer.scrollTop = logsContainer.scrollHeight;
                
                // Ограничиваем количество логов (максимум 50)
                const logs = logsContainer.children;
                if (logs.length > 50) {
                    logsContainer.removeChild(logs[0]);
                }
            }
            
            async function selectDownloadPath() {
                try {
                    // Для macOS используем встроенный диалог
                    if (window.showDirectoryPicker) {
                        const dirHandle = await window.showDirectoryPicker();
                        document.getElementById('download_path').value = dirHandle.name;
                        addLog(`📁 Выбрана папка: ${dirHandle.name}`, 'info');
                    } else {
                        // Fallback: просто позволяем ввести путь
                        const path = prompt('Введите путь к папке для сохранения:', '~/Downloads/GodlySharing_Analysis');
                        if (path) {
                            document.getElementById('download_path').value = path;
                            addLog(`📁 Указана папка: ${path}`, 'info');
                        }
                    }
                } catch (error) {
                    console.log('Пользователь отменил выбор папки');
                }
            }
            
            async function startAnalysis() {
                const maxVideos = document.getElementById('max_videos').value;
                const includeTranscripts = document.getElementById('include_transcripts').checked;
                const notifyTelegram = document.getElementById('notify_telegram').checked;
                const downloadPath = document.getElementById('download_path').value || '~/Downloads/GodlySharing_Analysis';
                
                const startBtn = document.getElementById('startBtn');
                startBtn.disabled = true;
                startBtn.textContent = '⏳ Запуск...';
                
                try {
                    const response = await fetch(`${API_BASE}/analyze-channel`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            max_videos: parseInt(maxVideos),
                            include_transcripts: includeTranscripts,
                            notify_telegram: notifyTelegram,
                            download_path: downloadPath
                        })
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        console.log('Анализ запущен:', result);
                        
                        addLog(`🚀 Запуск анализа (макс. ${maxVideos} видео)`, 'info');
                        addLog(`🎙️ Транскрипция: ${includeTranscripts ? 'включена' : 'выключена'}`, 'info');
                        addLog(`📱 Telegram: ${notifyTelegram ? 'включен' : 'выключен'}`, 'info');
                        
                        // Начинаем отслеживание статуса
                        analysisInterval = setInterval(checkStatus, 2000);
                        checkStatus(); // Первая проверка сразу
                        
                    } else {
                        throw new Error('Ошибка запуска анализа');
                    }
                } catch (error) {
                    console.error('Ошибка:', error);
                    alert('Ошибка запуска анализа: ' + error.message);
                    startBtn.disabled = false;
                    startBtn.textContent = '🚀 Запустить анализ';
                }
            }
            
            async function checkStatus() {
                try {
                    const response = await fetch(`${API_BASE}/analysis-status`);
                    const status = await response.json();
                    
                    document.getElementById('status').textContent = getStatusText(status.status);
                    document.getElementById('current_video').textContent = status.current_video || '-';
                    
                    // Обновляем прогресс загрузки
                    const maxVideos = parseInt(document.getElementById('max_videos').value);
                    const downloadedCount = status.videos_downloaded || 0;
                    const transcribedCount = status.videos_processed || 0;
                    
                    document.getElementById('download_progress').textContent = `${downloadedCount}/${maxVideos}`;
                    document.getElementById('transcription_progress').textContent = `${transcribedCount}/${maxVideos}`;
                    
                    // Обновляем прогресс-бары
                    const downloadProgress = Math.min((downloadedCount / maxVideos) * 100, 100);
                    const transcriptionProgress = Math.min((transcribedCount / maxVideos) * 100, 100);
                    
                    document.getElementById('download_progress_bar').style.width = downloadProgress + '%';
                    document.getElementById('transcription_progress_bar').style.width = transcriptionProgress + '%';
                    
                    // Добавляем лог о текущем действии
                    if (status.current_video && status.current_video !== '-') {
                        addLog(status.current_video, 'info');
                    }
                    
                    // Обновляем промежуточные результаты в реальном времени
                    if (status.status === 'running') {
                        updateIntermediateResults(status);
                        console.log('Промежуточные результаты:', status.intermediate_stats);
                    }
                    
                    if (status.status === 'completed') {
                        clearInterval(analysisInterval);
                        document.getElementById('startBtn').disabled = false;
                        document.getElementById('startBtn').textContent = '🚀 Запустить анализ';
                        
                        // Принудительно устанавливаем 100% для обоих прогресс-баров
                        document.getElementById('download_progress_bar').style.width = '100%';
                        document.getElementById('transcription_progress_bar').style.width = '100%';
                        
                        document.getElementById('resultsPanel').style.display = 'block';
                        addLog('✅ Анализ успешно завершен!', 'success');
                        loadResults();
                    } else if (status.status === 'error') {
                        clearInterval(analysisInterval);
                        document.getElementById('startBtn').disabled = false;
                        document.getElementById('startBtn').textContent = '🚀 Запустить анализ';
                        addLog('❌ Ошибка: ' + (status.error || 'Неизвестная ошибка'), 'error');
                        alert('Ошибка анализа: ' + (status.error || 'Неизвестная ошибка'));
                    }
                } catch (error) {
                    console.error('Ошибка проверки статуса:', error);
                }
            }
            
            function getStatusText(status) {
                const statusMap = {
                    'idle': '⏸️ Ожидание',
                    'running': '🔄 Выполняется',
                    'completed': '✅ Завершен',
                    'error': '❌ Ошибка'
                };
                return statusMap[status] || status;
            }
            
            function updateIntermediateResults(status) {
                // Показываем панель результатов если еще не показана
                const resultsPanel = document.getElementById('resultsPanel');
                if (resultsPanel.style.display === 'none' || !resultsPanel.style.display) {
                    resultsPanel.style.display = 'block';
                }
                
                // Обновляем промежуточные данные
                const maxVideos = parseInt(document.getElementById('max_videos').value);
                const downloadedCount = status.videos_downloaded || 0;
                const transcribedCount = status.videos_processed || 0;
                
                // Используем промежуточную статистику если доступна
                const stats = status.intermediate_stats || {};
                
                // Создаем промежуточный HTML с текущими данными
                const intermediateHTML = `
                    <div class="status-item">
                        <span>📥 Загружено метаданных:</span>
                        <span>${downloadedCount} из ${maxVideos}</span>
                    </div>
                    <div class="status-item">
                        <span>🎙️ Транскрибировано:</span>
                        <span>${transcribedCount} из ${Math.min(3, maxVideos)}</span>
                    </div>
                    ${stats.processed_videos ? `
                    <div class="status-item">
                        <span>📊 Обработано видео:</span>
                        <span>${stats.processed_videos}</span>
                    </div>
                    <div class="status-item">
                        <span>👀 Средние просмотры:</span>
                        <span>${(stats.avg_views || 0).toLocaleString()}</span>
                    </div>
                    <div class="status-item">
                        <span>👍 Средние лайки:</span>
                        <span>${(stats.avg_likes || 0).toLocaleString()}</span>
                    </div>
                    ` : ''}
                    <div class="status-item" style="margin-top: 10px; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 5px;">
                        <span>🔄 Анализ в процессе...</span>
                        <span>Данные обновляются в реальном времени</span>
                    </div>
                `;
                
                document.getElementById('results').innerHTML = intermediateHTML;
            }
            
            async function loadResults() {
                try {
                    const response = await fetch(`${API_BASE}/analysis-results`);
                    const results = await response.json();
                    
                    // Данные лежат в корне JSON, не в summary
                    const resultsHTML = `
                        <div class="status-item">
                            <span>Всего видео:</span>
                            <span>${results.total_videos || 0}</span>
                        </div>
                        <div class="status-item">
                            <span>Средние просмотры:</span>
                            <span>${(results.avg_views || 0).toLocaleString()}</span>
                        </div>
                        <div class="status-item">
                            <span>Средние лайки:</span>
                            <span>${(results.avg_likes || 0).toLocaleString()}</span>
                        </div>
                        <div class="status-item">
                            <span>Лучшее видео:</span>
                            <span>${results.best_performing_videos?.[0]?.snippet?.title || 'Не определено'}</span>
                        </div>
                        <div class="status-item">
                            <span>Средняя длительность:</span>
                            <span>${results.avg_duration || 0} сек</span>
                        </div>
                        <div class="status-item">
                            <span>Топ ключевые слова:</span>
                            <span>${Object.keys(results.top_keywords || {}).slice(0,3).join(', ') || 'Нет данных'}</span>
                        </div>
                    `;
                    
                    document.getElementById('results').innerHTML = resultsHTML;
                } catch (error) {
                    console.error('Ошибка загрузки результатов:', error);
                    document.getElementById('results').innerHTML = '<p>Ошибка загрузки результатов</p>';
                }
            }
            
            async function downloadReport() {
                try {
                    const response = await fetch(`${API_BASE}/analysis-report`);
                    const data = await response.json();
                    
                    const blob = new Blob([data.report], { type: 'text/markdown' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'godly_sharing_analysis_report.md';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                } catch (error) {
                    console.error('Ошибка скачивания отчета:', error);
                    alert('Ошибка скачивания отчета');
                }
            }
            
            // Проверяем статус при загрузке страницы
            checkStatus();
        </script>
    </body>
    </html>
    """
    
    return HTMLResponse(content=html_content)

@app.post("/analyze-channel")
async def start_analysis(request: AnalysisRequest, background_tasks: BackgroundTasks):
    """Запуск анализа канала"""
    
    if analysis_state["status"] == "running":
        raise HTTPException(status_code=409, detail="Анализ уже выполняется")
    
    analysis_state.update({
        "status": "running",
        "started_at": datetime.now().isoformat(),
        "videos_downloaded": 0,
        "videos_processed": 0,
        "current_video": "Подготовка...",
        "results": None,
        "error": None,
        "intermediate_stats": None
    })
    
    background_tasks.add_task(run_channel_analysis, request)
    
    return {
        "message": "Анализ канала @GodlySharing запущен",
        "status": "running",
        "estimated_time": f"{request.max_videos * 2} минут",
        "tailscale_secure": True
    }

@app.get("/analysis-status")
async def get_analysis_status():
    """Получение статуса анализа"""
    return AnalysisStatus(**analysis_state)

@app.get("/analysis-results")
async def get_analysis_results():
    """Получение результатов анализа"""
    
    if analysis_state["status"] != "completed":
        raise HTTPException(status_code=404, detail="Анализ не завершен")
    
    results_file = Path("analysis_data/analysis_report.json")
    if not results_file.exists():
        raise HTTPException(status_code=404, detail="Файл результатов не найден")
    
    try:
        with open(results_file, 'r', encoding='utf-8') as f:
            results = json.load(f)
        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Ошибка чтения результатов: {e}")

@app.get("/analysis-report")
async def get_readable_report():
    """Получение читаемого отчета"""
    
    report_file = Path("analysis_data/ANALYSIS_REPORT.md")
    if not report_file.exists():
        raise HTTPException(status_code=404, detail="Отчет не найден")
    
    try:
        with open(report_file, 'r', encoding='utf-8') as f:
            content = f.read()
        return {"report": content}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Ошибка чтения отчета: {e}")

async def run_channel_analysis(request: AnalysisRequest):
    """Фоновая задача анализа канала"""
    
    try:
        print(f"🚀 Запуск анализа канала (макс. {request.max_videos} видео)")
        
        from youtube_analyzer import GodlySharingAnalyzer
        analyzer = GodlySharingAnalyzer()
        
        # 1. Поиск канала
        analysis_state["current_video"] = "Поиск канала @GodlySharing..."
        channel_id = analyzer.get_channel_id()
        if not channel_id:
            raise Exception("Канал @GodlySharing не найден")
        
        # 2. Получение видео
        analysis_state["current_video"] = "Получение списка видео..."
        videos = analyzer.get_all_videos(channel_id, max_results=request.max_videos)
        if not videos:
            raise Exception("Видео не найдены")
        
        # 3. Статистика (с прогрессом загрузки)
        analysis_state["current_video"] = "Получение статистики..."
        video_ids = [video['id']['videoId'] for video in videos]
        video_stats = analyzer.get_video_stats(video_ids)
        
        videos_with_stats = []
        total_views = 0
        total_likes = 0
        
        for i, (video, stats) in enumerate(zip(videos, video_stats), 1):
            combined = {**video, **stats}
            videos_with_stats.append(combined)
            
            # Накапливаем статистику для промежуточных результатов
            try:
                total_views += int(stats.get('viewCount', '0'))
                total_likes += int(stats.get('likeCount', '0'))
            except (ValueError, TypeError):
                # Если данные не числовые - пропускаем
                pass
            
            # Обновляем прогресс загрузки метаданных
            analysis_state["videos_downloaded"] = i
            analysis_state["current_video"] = f"Загружено метаданных: {i}/{len(videos)}"
            
            # Сохраняем промежуточную статистику
            analysis_state["intermediate_stats"] = {
                "processed_videos": i,
                "total_views": total_views,
                "avg_views": total_views // i if i > 0 else 0,
                "total_likes": total_likes,
                "avg_likes": total_likes // i if i > 0 else 0
            }
        
        # 4. Транскрибация (ограниченная)
        videos_with_transcripts = []
        
        if request.include_transcripts:
            print("🎤 Начинаем транскрибацию...")
            transcribe_count = min(3, len(videos_with_stats))  # Только 3 видео для скорости
            
            for i, video in enumerate(videos_with_stats[:transcribe_count], 1):
                video_id = video['id']
                title = video.get('snippet', {}).get('title', 'Без названия')
                
                analysis_state["current_video"] = f"Транскрибация {i}/{transcribe_count}: {title[:30]}..."
                analysis_state["videos_processed"] = i
                
                audio_file = analyzer.download_audio(video_id, title)
                if audio_file:
                    transcript = analyzer.transcribe_audio(audio_file, video_id)
                    video['transcript'] = transcript
                
                videos_with_transcripts.append(video)
            
            videos_with_transcripts.extend(videos_with_stats[transcribe_count:])
        else:
            videos_with_transcripts = videos_with_stats
        
        # 5. Анализ паттернов
        analysis_state["current_video"] = "Анализ паттернов успешного контента..."
        results = analyzer.analyze_patterns(videos_with_transcripts)
        
        # 6. Сохранение результатов
        analysis_state["current_video"] = "Сохранение результатов..."
        Path("analysis_data").mkdir(exist_ok=True)
        
        with open("analysis_data/analysis_report.json", 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        analyzer.create_readable_report(results)
        
        # 7. Уведомление в Telegram
        if request.notify_telegram:
            await send_telegram_notification(results)
        
        analysis_state.update({
            "status": "completed",
            "current_video": "Анализ завершен!",
            "results": results
        })
        
        print("✅ Анализ канала @GodlySharing завершен успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка анализа: {e}")
        analysis_state.update({
            "status": "error",
            "error": str(e),
            "current_video": f"Ошибка: {e}"
        })

async def send_telegram_notification(results):
    """Отправка уведомления в Telegram"""
    
    try:
        config_file = Path("telegram_config.json")
        if not config_file.exists():
            print("⚠️ Конфигурация Telegram не найдена")
            return
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        bot_token = config.get('bot_token')
        chat_id = config.get('group_chat_id')
        
        if not bot_token or not chat_id:
            print("⚠️ Неполная конфигурация Telegram")
            return
        
        total_videos = results.get('summary', {}).get('total_videos', 0)
        avg_views = results.get('summary', {}).get('average_views', 0)
        
        message = f"""
🎬 Анализ канала @GodlySharing завершен!

📊 Результаты:
• Проанализировано видео: {total_videos}
• Средние просмотры: {avg_views:,}

🔗 Безопасный доступ через Tailscale:
https://ikas-macbook-pro.tailf48d3.ts.net/dashboard

🔐 Защищенное соединение активно
        """
        
        curl_cmd = [
            'curl', '-X', 'POST',
            f'https://api.telegram.org/bot{bot_token}/sendMessage',
            '-H', 'Content-Type: application/json',
            '-d', json.dumps({
                'chat_id': chat_id,
                'text': message.strip(),
                'parse_mode': 'HTML'
            })
        ]
        
        subprocess.run(curl_cmd, capture_output=True)
        print("📱 Уведомление отправлено в Telegram")
        
    except Exception as e:
        print(f"⚠️ Ошибка отправки Telegram уведомления: {e}")

def main():
    """Запуск сервера для Tailscale Serve"""
    
    print("🔐 Запуск GodlySharing Analyzer для Tailscale...")
    print("💡 Используйте: tailscale serve 8080 http://localhost:8080")
    print("🌐 Доступ: https://ikas-macbook-pro.tailf48d3.ts.net")
    
    # Запускаем на localhost:8080 для Tailscale Serve
    uvicorn.run(
        app,
        host="127.0.0.1",  # Только локальный доступ
        port=8080,         # Порт для Tailscale Serve
        access_log=True,
        reload=False
    )

if __name__ == "__main__":
    main() 
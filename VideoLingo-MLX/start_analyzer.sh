#!/bin/bash

# Запуск системы анализа канала @GodlySharing
# Для ночного тестирования Икой

echo "🌙 Запуск системы анализа @GodlySharing"
echo "========================================"

# Проверяем Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 не найден"
    exit 1
fi

echo "✅ Python3 найден"

# Переходим в рабочую директорию
cd "$(dirname "$0")"

# Проверяем зависимости
echo "🔍 Проверяем зависимости..."

if ! python3 -c "import requests, whisper, yt_dlp, fastapi, uvicorn" 2>/dev/null; then
    echo "📦 Устанавливаем зависимости..."
    pip3 install -r requirements_analyzer.txt
    pip3 install fastapi uvicorn
else
    echo "✅ Все зависимости установлены"
fi

# Создаем директории
mkdir -p analysis_data
mkdir -p analysis_data/transcripts
mkdir -p analysis_data/audio

echo "📁 Директории созданы"

# Запускаем FastAPI сервер в фоне
echo "🚀 Запуск API сервера на порту 8001..."
python3 analyzer_api.py &
API_PID=$!

# Ждем запуска сервера
sleep 3

# Проверяем, что сервер запустился
if curl -s http://localhost:8001/ > /dev/null; then
    echo "✅ API сервер запущен (PID: $API_PID)"
else
    echo "❌ Ошибка запуска API сервера"
    kill $API_PID 2>/dev/null
    exit 1
fi

# Открываем веб-интерфейс
echo "🌐 Открываем веб-интерфейс..."

# Для macOS
if [[ "$OSTYPE" == "darwin"* ]]; then
    open analyzer_dashboard.html
# Для Linux
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    xdg-open analyzer_dashboard.html
fi

echo ""
echo "🎯 Система анализа @GodlySharing запущена!"
echo ""
echo "📊 API: http://localhost:8001"
echo "📖 Документация: http://localhost:8001/docs"
echo "🌐 Веб-интерфейс: analyzer_dashboard.html"
echo ""
echo "⌨️  Команды:"
echo "   - Ctrl+C для остановки"
echo "   - kill $API_PID для остановки API"
echo ""
echo "🔥 Готов к анализу! Ика, тестируй!"

# Ждем сигнала остановки
trap "echo ''; echo '⏹️  Остановка системы...'; kill $API_PID 2>/dev/null; exit 0" INT TERM

# Показываем логи API
echo "📝 Логи API сервера:"
echo "==================="
wait $API_PID
#!/usr/bin/env python3
"""
YouTube Channel Analyzer for @GodlySharing
Анализ существующих видео канала для улучшения контента
"""

import json
import requests
import time
from datetime import datetime
import whisper
import yt_dlp
import os
import pandas as pd
from pathlib import Path

class GodlySharingAnalyzer:
    def __init__(self, api_key="AIzaSyAeI-U4Et6xrWx4IX5Hol14eZyJAk0UGfU", download_path=None):
        self.api_key = api_key
        self.channel_name = "@GodlySharing"
        self.base_url = "https://www.googleapis.com/youtube/v3"
        
        # Создаем директории для данных
        if download_path:
            # Расширяем тильду в пути
            self.data_dir = Path(download_path).expanduser()
        else:
            self.data_dir = Path("analysis_data")
        self.data_dir.mkdir(exist_ok=True, parents=True)
        
        self.transcripts_dir = self.data_dir / "transcripts"
        self.transcripts_dir.mkdir(exist_ok=True)
        
        self.audio_dir = self.data_dir / "audio"
        self.audio_dir.mkdir(exist_ok=True)
        
        # Загружаем Whisper для транскрибации
        print("🎤 Загружаем Whisper модель...")
        self.whisper_model = whisper.load_model("base")
        
    def get_channel_id(self):
        """Получаем ID канала по имени"""
        url = f"{self.base_url}/search"
        params = {
            'part': 'snippet',
            'q': self.channel_name,
            'type': 'channel',
            'key': self.api_key
        }
        
        response = requests.get(url, params=params)
        data = response.json()
        
        if 'items' in data and len(data['items']) > 0:
            channel_id = data['items'][0]['snippet']['channelId']
            print(f"✅ Найден канал: {channel_id}")
            return channel_id
        else:
            print("❌ Канал не найден")
            return None
    
    def get_all_videos(self, channel_id, max_results=50):
        """Получаем все видео канала"""
        print(f"📺 Получаем видео канала (макс. {max_results})...")
        
        url = f"{self.base_url}/search"
        params = {
            'part': 'snippet',
            'channelId': channel_id,
            'type': 'video',
            'order': 'date',
            'maxResults': min(max_results, 50),
            'key': self.api_key
        }
        
        all_videos = []
        next_page_token = None
        
        while len(all_videos) < max_results:
            if next_page_token:
                params['pageToken'] = next_page_token
                
            response = requests.get(url, params=params)
            data = response.json()
            
            if 'items' not in data:
                break
                
            all_videos.extend(data['items'])
            
            if 'nextPageToken' not in data:
                break
                
            next_page_token = data['nextPageToken']
            
            if len(all_videos) >= max_results:
                all_videos = all_videos[:max_results]
                break
        
        print(f"✅ Найдено {len(all_videos)} видео")
        return all_videos
    
    def get_video_stats(self, video_ids):
        """Получаем статистику видео"""
        print("📊 Получаем статистику видео...")
        
        # YouTube API позволяет запрашивать до 50 видео за раз
        video_stats = []
        
        for i in range(0, len(video_ids), 50):
            batch_ids = video_ids[i:i+50]
            ids_string = ','.join(batch_ids)
            
            url = f"{self.base_url}/videos"
            params = {
                'part': 'statistics,contentDetails,snippet',
                'id': ids_string,
                'key': self.api_key
            }
            
            response = requests.get(url, params=params)
            data = response.json()
            
            if 'items' in data:
                video_stats.extend(data['items'])
                
            time.sleep(0.1)  # Небольшая пауза между запросами
        
        print(f"✅ Получена статистика для {len(video_stats)} видео")
        return video_stats
    
    def download_audio(self, video_id, title):
        """Скачиваем аудио из видео для транскрибации"""
        audio_file = self.audio_dir / f"{video_id}.mp3"
        
        if audio_file.exists():
            print(f"⏭️ Аудио уже существует: {video_id}")
            return str(audio_file)
        
        try:
            ydl_opts = {
                'format': 'bestaudio/best',
                'outtmpl': str(self.audio_dir / f"{video_id}.%(ext)s"),
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'mp3',
                    'preferredquality': '192',
                }],
                'quiet': True
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([f"https://www.youtube.com/watch?v={video_id}"])
            
            print(f"✅ Скачано аудио: {title[:50]}...")
            return str(audio_file)
            
        except Exception as e:
            print(f"❌ Ошибка скачивания {video_id}: {e}")
            return None
    
    def transcribe_audio(self, audio_file, video_id):
        """Создаем транскрипцию аудио"""
        transcript_file = self.transcripts_dir / f"{video_id}.json"
        
        if transcript_file.exists():
            print(f"⏭️ Транскрипция уже существует: {video_id}")
            with open(transcript_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        try:
            print(f"🎤 Транскрибируем: {video_id}")
            result = self.whisper_model.transcribe(audio_file)
            
            transcript_data = {
                'video_id': video_id,
                'text': result['text'],
                'segments': result['segments'],
                'language': result['language'],
                'created_at': datetime.now().isoformat()
            }
            
            # Сохраняем транскрипцию
            with open(transcript_file, 'w', encoding='utf-8') as f:
                json.dump(transcript_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Транскрипция готова: {video_id}")
            return transcript_data
            
        except Exception as e:
            print(f"❌ Ошибка транскрибации {video_id}: {e}")
            return None
    
    def analyze_patterns(self, videos_with_transcripts):
        """Анализируем паттерны успешного контента (главный метод)"""
        return self.analyze_content_patterns(videos_with_transcripts)
    
    def analyze_content_patterns(self, videos_with_transcripts):
        """Анализируем паттерны успешного контента"""
        print("🔍 Анализируем паттерны контента...")
        
        analysis = {
            'total_videos': len(videos_with_transcripts),
            'avg_views': 0,
            'avg_likes': 0,
            'avg_duration': 0,
            'top_keywords': {},
            'successful_patterns': [],
            'content_themes': {},
            'performance_by_length': {},
            'best_performing_videos': []
        }
        
        total_views = 0
        total_likes = 0
        total_duration = 0
        all_text = ""
        
        # Сортируем по просмотрам
        sorted_videos = sorted(videos_with_transcripts, 
                             key=lambda x: int(x.get('statistics', {}).get('viewCount', 0)), 
                             reverse=True)
        
        for video in videos_with_transcripts:
            stats = video.get('statistics', {})
            content_details = video.get('contentDetails', {})
            transcript = video.get('transcript', {})
            
            views = int(stats.get('viewCount', 0))
            likes = int(stats.get('likeCount', 0))
            
            total_views += views
            total_likes += likes
            
            # Анализ длительности
            duration = content_details.get('duration', 'PT0S')
            # Простой парсер PT1M30S -> секунды
            duration_seconds = self.parse_duration(duration)
            total_duration += duration_seconds
            
            # Анализ текста
            if transcript and 'text' in transcript:
                all_text += " " + transcript['text']
        
        analysis['avg_views'] = total_views // len(videos_with_transcripts) if videos_with_transcripts else 0
        analysis['avg_likes'] = total_likes // len(videos_with_transcripts) if videos_with_transcripts else 0
        analysis['avg_duration'] = total_duration // len(videos_with_transcripts) if videos_with_transcripts else 0
        
        # Топ-10 самых успешных видео
        analysis['best_performing_videos'] = sorted_videos[:10]
        
        # Анализ ключевых слов (простой)
        words = all_text.lower().split()
        word_freq = {}
        for word in words:
            if len(word) > 3:  # Только слова длиннее 3 символов
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # Топ-20 ключевых слов
        analysis['top_keywords'] = dict(sorted(word_freq.items(), 
                                             key=lambda x: x[1], 
                                             reverse=True)[:20])
        
        return analysis
    
    def parse_duration(self, duration_str):
        """Парсим YouTube duration PT1M30S в секунды"""
        import re
        
        pattern = r'PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?'
        match = re.match(pattern, duration_str)
        
        if not match:
            return 0
        
        hours = int(match.group(1) or 0)
        minutes = int(match.group(2) or 0)
        seconds = int(match.group(3) or 0)
        
        return hours * 3600 + minutes * 60 + seconds
    
    def save_analysis_report(self, analysis, videos_data):
        """Сохраняем отчет анализа"""
        report_file = self.data_dir / "analysis_report.json"
        
        report = {
            'channel': self.channel_name,
            'analysis_date': datetime.now().isoformat(),
            'summary': analysis,
            'videos_data': videos_data
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ Отчет сохранен: {report_file}")
        
        # Создаем читаемый отчет
        self.create_readable_report(analysis)
    
    def create_readable_report(self, analysis):
        """Создаем читаемый отчет"""
        report_file = self.data_dir / "ANALYSIS_REPORT.md"
        
        report_content = f"""# 📊 Анализ канала @GodlySharing

## 🎯 Общая статистика

- **Всего видео проанализировано**: {analysis['total_videos']}
- **Средние просмотры**: {analysis['avg_views']:,}
- **Средние лайки**: {analysis['avg_likes']:,}
- **Средняя длительность**: {analysis['avg_duration']} секунд

## 🔥 Топ-10 самых успешных видео

"""
        
        for i, video in enumerate(analysis['best_performing_videos'], 1):
            title = video.get('snippet', {}).get('title', 'Без названия')
            views = int(video.get('statistics', {}).get('viewCount', 0))
            video_id = video.get('id', '')
            
            report_content += f"{i}. **{title}**\n"
            report_content += f"   - Просмотры: {views:,}\n"
            report_content += f"   - Ссылка: https://youtu.be/{video_id}\n\n"
        
        report_content += "## 🔑 Топ ключевые слова\n\n"
        
        for word, count in list(analysis['top_keywords'].items())[:10]:
            report_content += f"- **{word}**: {count} упоминаний\n"
        
        report_content += f"""

## 💡 Рекомендации для улучшения

### На основе анализа успешных видео:

1. **Оптимальная длительность**: {analysis['avg_duration']} секунд
2. **Популярные темы**: {', '.join(list(analysis['top_keywords'].keys())[:5])}
3. **Средняя вовлеченность**: {analysis['avg_likes']/analysis['avg_views']*100:.2f}% лайков от просмотров

---
*Анализ создан {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"✅ Читаемый отчет создан: {report_file}")

def main():
    print("🚀 Запуск анализа канала @GodlySharing")
    print("=" * 50)
    
    analyzer = GodlySharingAnalyzer()
    
    # 1. Получаем ID канала
    channel_id = analyzer.get_channel_id()
    if not channel_id:
        return
    
    # 2. Получаем список видео
    videos = analyzer.get_all_videos(channel_id, max_results=20)  # Начнем с 20 видео
    
    if not videos:
        print("❌ Видео не найдены")
        return
    
    # 3. Получаем статистику
    video_ids = [video['id']['videoId'] for video in videos]
    video_stats = analyzer.get_video_stats(video_ids)
    
    # 4. Объединяем данные
    videos_with_stats = []
    for video, stats in zip(videos, video_stats):
        combined = {**video, **stats}
        videos_with_stats.append(combined)
    
    # 5. Скачиваем аудио и создаем транскрипции
    print("\n🎤 Начинаем транскрибацию видео...")
    
    videos_with_transcripts = []
    for i, video in enumerate(videos_with_stats[:5], 1):  # Первые 5 видео для теста
        video_id = video['id']
        title = video.get('snippet', {}).get('title', 'Без названия')
        
        print(f"\n[{i}/5] Обрабатываем: {title[:50]}...")
        
        # Скачиваем аудио
        audio_file = analyzer.download_audio(video_id, title)
        
        if audio_file:
            # Создаем транскрипцию
            transcript = analyzer.transcribe_audio(audio_file, video_id)
            video['transcript'] = transcript
        
        videos_with_transcripts.append(video)
    
    # 6. Анализируем паттерны
    analysis = analyzer.analyze_content_patterns(videos_with_transcripts)
    
    # 7. Сохраняем отчет
    analyzer.save_analysis_report(analysis, videos_with_transcripts)
    
    print("\n✅ Анализ завершен!")
    print(f"📊 Проанализировано: {len(videos_with_transcripts)} видео")
    print(f"📁 Данные сохранены в: {analyzer.data_dir}")
    print(f"📝 Отчет: {analyzer.data_dir}/ANALYSIS_REPORT.md")

if __name__ == "__main__":
    main()
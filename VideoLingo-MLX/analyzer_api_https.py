#!/usr/bin/env python3
"""
HTTPS FastAPI сервер для анализа канала @GodlySharing
Настроен для работы через Tailscale с валидными SSL сертификатами
"""

from fastapi import FastAPI, BackgroundTasks, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from pydantic import BaseModel
from typing import Optional
import json
import asyncio
from datetime import datetime
from pathlib import Path
import subprocess
import sys
import ssl
import uvicorn

app = FastAPI(title="GodlySharing Channel Analyzer API (HTTPS)", version="1.0.0")

# Настройка CORS для Tailscale доменов
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "https://ikas-macbook-pro.tailf48d3.ts.net",
        "https://************",
        "http://localhost:*",
        "https://localhost:*"
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

class AnalysisRequest(BaseModel):
    max_videos: int = 20
    include_transcripts: bool = True
    notify_telegram: bool = True

class AnalysisStatus(BaseModel):
    status: str
    started_at: Optional[str] = None
    videos_processed: int = 0
    current_video: str = ""
    estimated_completion: str = ""

# Глобальное состояние анализа
analysis_state = {
    "status": "idle",  # idle, running, completed, error
    "started_at": None,
    "videos_processed": 0,
    "current_video": "",
    "results": None,
    "error": None
}

@app.get("/")
async def root():
    return {
        "service": "GodlySharing Channel Analyzer (HTTPS)",
        "version": "1.0.0",
        "status": "active",
        "tailscale_domain": "ikas-macbook-pro.tailf48d3.ts.net",
        "secure": True,
        "endpoints": {
            "dashboard": "/dashboard",
            "analyze": "/analyze-channel",
            "status": "/analysis-status",
            "results": "/analysis-results"
        }
    }

@app.get("/dashboard", response_class=HTMLResponse)
async def get_dashboard():
    """Возвращает веб-интерфейс анализатора"""
    dashboard_file = Path("analyzer_dashboard.html")
    
    if not dashboard_file.exists():
        return HTMLResponse("""
        <html>
            <head><title>GodlySharing Analyzer</title></head>
            <body>
                <h1>🚀 GodlySharing Channel Analyzer</h1>
                <p>Secure HTTPS connection via Tailscale</p>
                <p>Domain: ikas-macbook-pro.tailf48d3.ts.net</p>
                <p>Dashboard file not found. Please check analyzer_dashboard.html</p>
            </body>
        </html>
        """)
    
    try:
        with open(dashboard_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Обновляем URL в HTML для HTTPS Tailscale
        content = content.replace(
            'http://localhost:8001',
            'https://ikas-macbook-pro.tailf48d3.ts.net'
        )
        
        return HTMLResponse(content)
    except Exception as e:
        return HTMLResponse(f"<h1>Error loading dashboard: {e}</h1>")

@app.post("/analyze-channel")
async def start_analysis(request: AnalysisRequest, background_tasks: BackgroundTasks):
    """Запуск анализа канала в фоновом режиме"""
    
    if analysis_state["status"] == "running":
        raise HTTPException(status_code=409, detail="Анализ уже выполняется")
    
    # Сбрасываем состояние
    analysis_state.update({
        "status": "running",
        "started_at": datetime.now().isoformat(),
        "videos_processed": 0,
        "current_video": "Подготовка...",
        "results": None,
        "error": None
    })
    
    # Запускаем анализ в фоне
    background_tasks.add_task(run_channel_analysis, request)
    
    return {
        "message": "Анализ канала @GodlySharing запущен",
        "status": "running",
        "estimated_time": f"{request.max_videos * 2} минут",
        "check_status": "/analysis-status",
        "secure_connection": True,
        "tailscale_domain": "ikas-macbook-pro.tailf48d3.ts.net"
    }

@app.get("/analysis-status")
async def get_analysis_status():
    """Получение текущего статуса анализа"""
    return AnalysisStatus(**analysis_state)

@app.get("/analysis-results")
async def get_analysis_results():
    """Получение результатов анализа"""
    
    if analysis_state["status"] != "completed":
        raise HTTPException(status_code=404, detail="Анализ не завершен")
    
    # Читаем результаты из файла
    results_file = Path("analysis_data/analysis_report.json")
    
    if not results_file.exists():
        raise HTTPException(status_code=404, detail="Файл результатов не найден")
    
    try:
        with open(results_file, 'r', encoding='utf-8') as f:
            results = json.load(f)
        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Ошибка чтения результатов: {e}")

@app.get("/analysis-report")
async def get_readable_report():
    """Получение читаемого отчета"""
    
    report_file = Path("analysis_data/ANALYSIS_REPORT.md")
    
    if not report_file.exists():
        raise HTTPException(status_code=404, detail="Отчет не найден")
    
    try:
        with open(report_file, 'r', encoding='utf-8') as f:
            content = f.read()
        return {"report": content}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Ошибка чтения отчета: {e}")

async def run_channel_analysis(request: AnalysisRequest):
    """Фоновая задача для анализа канала"""
    
    try:
        print(f"🚀 Запуск анализа канала (макс. {request.max_videos} видео)")
        
        # Импортируем анализатор
        from youtube_analyzer import GodlySharingAnalyzer
        
        analyzer = GodlySharingAnalyzer()
        
        # Обновляем статус
        analysis_state["current_video"] = "Поиск канала..."
        
        # 1. Получаем ID канала
        channel_id = analyzer.get_channel_id()
        if not channel_id:
            raise Exception("Канал @GodlySharing не найден")
        
        # 2. Получаем видео
        analysis_state["current_video"] = "Получение списка видео..."
        videos = analyzer.get_all_videos(channel_id, max_results=request.max_videos)
        
        if not videos:
            raise Exception("Видео не найдены")
        
        # 3. Получаем статистику
        analysis_state["current_video"] = "Получение статистики..."
        video_ids = [video['id']['videoId'] for video in videos]
        video_stats = analyzer.get_video_stats(video_ids)
        
        # Объединяем данные
        videos_with_stats = []
        for video, stats in zip(videos, video_stats):
            combined = {**video, **stats}
            videos_with_stats.append(combined)
        
        # 4. Транскрибация (если запрошена)
        videos_with_transcripts = []
        
        if request.include_transcripts:
            print("🎤 Начинаем транскрибацию...")
            
            # Ограничиваем транскрибацию первыми 5 видео для скорости
            transcribe_count = min(5, len(videos_with_stats))
            
            for i, video in enumerate(videos_with_stats[:transcribe_count], 1):
                video_id = video['id']
                title = video.get('snippet', {}).get('title', 'Без названия')
                
                analysis_state["current_video"] = f"Транскрибация {i}/{transcribe_count}: {title[:30]}..."
                analysis_state["videos_processed"] = i
                
                # Скачиваем аудио
                audio_file = analyzer.download_audio(video_id, title)
                
                if audio_file:
                    # Создаем транскрипцию
                    transcript = analyzer.transcribe_audio(audio_file, video_id)
                    video['transcript'] = transcript
                
                videos_with_transcripts.append(video)
            
            # Добавляем остальные видео без транскрипции
            videos_with_transcripts.extend(videos_with_stats[transcribe_count:])
        else:
            videos_with_transcripts = videos_with_stats
        
        # 5. Анализируем паттерны
        analysis_state["current_video"] = "Анализ паттернов успешного контента..."
        
        results = analyzer.analyze_patterns(videos_with_transcripts)
        
        # 6. Сохраняем результаты
        analysis_state["current_video"] = "Сохранение результатов..."
        
        # Создаем папку для результатов
        Path("analysis_data").mkdir(exist_ok=True)
        
        # Сохраняем JSON
        with open("analysis_data/analysis_report.json", 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # Создаем читаемый отчет
        analyzer.create_readable_report(results)
        
        # 7. Отправляем уведомление в Telegram (если запрошено)
        if request.notify_telegram:
            await send_telegram_notification(results)
        
        # Обновляем состояние
        analysis_state.update({
            "status": "completed",
            "current_video": "Анализ завершен!",
            "results": results
        })
        
        print("✅ Анализ канала @GodlySharing завершен успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка анализа: {e}")
        analysis_state.update({
            "status": "error",
            "error": str(e),
            "current_video": f"Ошибка: {e}"
        })

async def send_telegram_notification(results):
    """Отправка уведомления в Telegram о завершении анализа"""
    
    try:
        # Читаем конфигурацию Telegram
        config_file = Path("telegram_config.json")
        if not config_file.exists():
            print("⚠️ Конфигурация Telegram не найдена, пропускаем уведомление")
            return
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        bot_token = config.get('bot_token')
        chat_id = config.get('group_chat_id')
        
        if not bot_token or not chat_id:
            print("⚠️ Неполная конфигурация Telegram, пропускаем уведомление")
            return
        
        # Формируем сообщение
        total_videos = results.get('summary', {}).get('total_videos', 0)
        avg_views = results.get('summary', {}).get('average_views', 0)
        
        message = f"""
🎬 Анализ канала @GodlySharing завершен!

📊 Результаты:
• Проанализировано видео: {total_videos}
• Средние просмотры: {avg_views:,}
• Отчет готов: https://ikas-macbook-pro.tailf48d3.ts.net/analysis-report

🔗 Доступ к результатам:
https://ikas-macbook-pro.tailf48d3.ts.net/dashboard
        """
        
        # Отправляем через subprocess (простой способ)
        curl_cmd = [
            'curl', '-X', 'POST',
            f'https://api.telegram.org/bot{bot_token}/sendMessage',
            '-H', 'Content-Type: application/json',
            '-d', json.dumps({
                'chat_id': chat_id,
                'text': message.strip(),
                'parse_mode': 'HTML'
            })
        ]
        
        subprocess.run(curl_cmd, capture_output=True)
        print("📱 Уведомление отправлено в Telegram")
        
    except Exception as e:
        print(f"⚠️ Ошибка отправки Telegram уведомления: {e}")

def main():
    """Запуск HTTPS сервера через Tailscale"""
    
    print("🔐 Запуск GodlySharing Analyzer с HTTPS через Tailscale...")
    print(f"🌐 Домен: https://ikas-macbook-pro.tailf48d3.ts.net")
    print(f"📱 IP: https://************")
    
    # Tailscale автоматически предоставляет валидные SSL сертификаты
    # Используем стандартный HTTPS порт 443
    uvicorn.run(
        app, 
        host="0.0.0.0",  # Слушаем все интерфейсы
        port=443,        # Стандартный HTTPS порт
        ssl_keyfile=None,    # Tailscale управляет сертификатами
        ssl_certfile=None,   # Tailscale управляет сертификатами
        access_log=True,
        reload=False
    )

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
"""
Простой тест HTTPS через Tailscale
Минимальная версия для проверки подключения
"""

try:
    from fastapi import FastAPI
    from fastapi.responses import HTMLResponse
    import uvicorn
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False

if FASTAPI_AVAILABLE:
    app = FastAPI(title="Tailscale HTTPS Test", version="1.0.0")
    
    @app.get("/", response_class=HTMLResponse)
    async def root():
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Tailscale HTTPS Test</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    max-width: 800px;
                    margin: 50px auto;
                    padding: 20px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    text-align: center;
                }
                .container {
                    background: rgba(255,255,255,0.1);
                    border-radius: 15px;
                    padding: 30px;
                    backdrop-filter: blur(10px);
                }
                h1 { font-size: 3rem; margin-bottom: 20px; }
                .status { font-size: 1.5rem; margin: 20px 0; }
                .info { background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🔐 Tailscale HTTPS Test</h1>
                <div class="status">✅ Соединение установлено!</div>
                
                <div class="info">
                    <strong>🌐 Домен:</strong> ikas-macbook-pro.tailf48d3.ts.net
                </div>
                <div class="info">
                    <strong>📱 IP:</strong> ************
                </div>
                <div class="info">
                    <strong>🔒 Протокол:</strong> HTTPS (SSL активен)
                </div>
                <div class="info">
                    <strong>🎯 Статус:</strong> Готов к работе
                </div>
                
                <div style="margin-top: 30px;">
                    <h3>🎬 GodlySharing Analyzer</h3>
                    <p>Система анализа YouTube канала готова к запуску</p>
                    <p><strong>Следующий шаг:</strong> Установка зависимостей</p>
                </div>
            </div>
        </body>
        </html>
        """
        return HTMLResponse(content=html_content)
    
    @app.get("/test")
    async def test():
        return {
            "status": "success",
            "message": "Tailscale HTTPS работает!",
            "domain": "ikas-macbook-pro.tailf48d3.ts.net",
            "ip": "************",
            "ssl": "active"
        }

def main():
    if not FASTAPI_AVAILABLE:
        print("❌ FastAPI не установлен")
        print("💡 Установите: pip3 install fastapi uvicorn")
        return
    
    print("🔐 Запуск тестового HTTPS сервера через Tailscale...")
    print("🌐 Домен: https://ikas-macbook-pro.tailf48d3.ts.net")
    print("📱 IP: https://************")
    print("💡 Используйте: tailscale serve https:443 http://localhost:8080")
    
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8080,
        access_log=True,
        reload=False
    )

if __name__ == "__main__":
    main() 
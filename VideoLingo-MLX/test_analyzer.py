#!/usr/bin/env python3
"""
Тестовый запуск анализатора @GodlySharing
Для ночного тестирования Икой
"""

import sys
import os
from pathlib import Path

def test_analyzer():
    print("🌙 Ночное тестирование анализатора @GodlySharing")
    print("=" * 50)
    
    # Проверяем зависимости
    try:
        import requests
        import whisper
        import yt_dlp
        print("✅ Все зависимости установлены")
    except ImportError as e:
        print(f"❌ Не хватает зависимости: {e}")
        print("💡 Установите: pip install -r requirements_analyzer.txt")
        return False
    
    # Проверяем YouTube API ключ
    api_key = "AIzaSyAeI-U4Et6xrWx4IX5Hol14eZyJAk0UGfU"
    
    # Тестовый запрос к YouTube API
    test_url = f"https://www.googleapis.com/youtube/v3/search?part=snippet&q=@GodlySharing&type=channel&key={api_key}"
    
    try:
        response = requests.get(test_url)
        if response.status_code == 200:
            print("✅ YouTube API работает")
        else:
            print(f"❌ YouTube API ошибка: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Ошибка подключения к YouTube API: {e}")
        return False
    
    # Запускаем анализатор
    try:
        from youtube_analyzer import GodlySharingAnalyzer
        
        analyzer = GodlySharingAnalyzer()
        
        # Тест 1: Получение ID канала
        print("\n🔍 Тест 1: Поиск канала...")
        channel_id = analyzer.get_channel_id()
        
        if not channel_id:
            print("❌ Канал не найден")
            return False
        
        # Тест 2: Получение видео
        print("\n📺 Тест 2: Получение видео...")
        videos = analyzer.get_all_videos(channel_id, max_results=5)
        
        if not videos:
            print("❌ Видео не найдены")
            return False
        
        print(f"✅ Найдено {len(videos)} видео")
        
        # Показываем первые 3 видео
        for i, video in enumerate(videos[:3], 1):
            title = video['snippet']['title']
            video_id = video['id']['videoId']
            print(f"   {i}. {title[:50]}... (ID: {video_id})")
        
        print("\n✅ Базовое тестирование прошло успешно!")
        print("🚀 Готов к полному анализу!")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка анализатора: {e}")
        return False

def run_quick_analysis():
    """Быстрый анализ 3 видео для теста"""
    print("\n🚀 Запуск быстрого анализа (3 видео)...")
    
    try:
        from youtube_analyzer import main as run_analyzer
        
        # Модифицируем для быстрого теста
        os.environ['QUICK_TEST'] = '1'
        run_analyzer()
        
        print("\n✅ Быстрый анализ завершен!")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка быстрого анализа: {e}")
        return False

if __name__ == "__main__":
    print("🎯 Выберите режим тестирования:")
    print("1. Базовое тестирование (проверка подключений)")
    print("2. Быстрый анализ (3 видео)")
    print("3. Полный анализ")
    
    choice = input("\nВведите номер (1-3): ").strip()
    
    if choice == "1":
        success = test_analyzer()
        if success:
            print("\n🎉 Система готова к работе!")
        else:
            print("\n❌ Требуется настройка")
            
    elif choice == "2":
        if test_analyzer():
            run_quick_analysis()
        else:
            print("\n❌ Сначала исправьте ошибки базового тестирования")
            
    elif choice == "3":
        if test_analyzer():
            print("\n🚀 Запуск полного анализа...")
            from youtube_analyzer import main
            main()
        else:
            print("\n❌ Сначала исправьте ошибки базового тестирования")
            
    else:
        print("❌ Неверный выбор")
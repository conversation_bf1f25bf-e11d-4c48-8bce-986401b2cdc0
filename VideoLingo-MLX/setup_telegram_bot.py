#!/usr/bin/env python3
"""
Telegram Bot Setup for GodlySharing YouTube Automation
Настройка Telegram бота для автоматизации YouTube канала @GodlySharing
"""

import json
import requests
import os
from datetime import datetime

class TelegramBotSetup:
    def __init__(self, config_file="telegram_config.json"):
        with open(config_file, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
        
        self.bot_token = os.getenv('TELEGRAM_BOT_TOKEN') or self.config['telegram_bot_config'].get('bot_token')
        if not self.bot_token:
            print("❌ TELEGRAM_BOT_TOKEN не найден!")
            print("💡 Установите токен в переменные окружения или config файл")
            exit(1)
        
        # Используем group_chat_id вместо channel_id если доступен
        self.chat_id = self.config['telegram_bot_config'].get('group_chat_id') or self.config['telegram_bot_config']['channel_id']
    
    def test_bot_connection(self):
        """Тестирование подключения к Telegram Bot API"""
        url = f"https://api.telegram.org/bot{self.bot_token}/getMe"
        
        try:
            response = requests.get(url)
            if response.status_code == 200:
                bot_info = response.json()
                print("✅ Telegram Bot подключен успешно!")
                print(f"🤖 Имя бота: {bot_info['result']['first_name']}")
                print(f"📛 Username: @{bot_info['result']['username']}")
                return True
            else:
                print(f"❌ Ошибка подключения: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Ошибка: {e}")
            return False
    
    def send_test_message(self):
        """Отправка тестового сообщения"""
        chat_id = self.chat_id
        
        test_message = f"""🧪 Тест MLX VideoLingo Bot
        
✅ Подключение к Telegram: OK
🔑 YouTube API: Настроен
🎬 MLX Engine: Готов к работе
⏰ Время: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🚀 Автоматизация @GodlySharing готова к запуску!
🤖 Бот настроен и протестирован"""
        
        url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
        
        payload = {
            'chat_id': chat_id,
            'text': test_message,
            'parse_mode': 'HTML'
        }
        
        try:
            response = requests.post(url, json=payload)
            if response.status_code == 200:
                print("✅ Тестовое сообщение отправлено успешно!")
                return True
            else:
                print(f"❌ Ошибка отправки: {response.status_code}")
                print(f"📄 Ответ: {response.text}")
                return False
        except Exception as e:
            print(f"❌ Ошибка: {e}")
            return False
    
    def setup_webhook_handlers(self):
        """Настройка webhook обработчиков для N8N"""
        webhooks = self.config['n8n_webhook_urls']
        
        print("🔗 Настройка webhook URLs для N8N:")
        for name, url in webhooks.items():
            print(f"   {name}: {url}")
        
        # Здесь можно добавить автоматическую настройку webhook в N8N
        print("💡 Добавьте эти URL в ваш N8N workflow для уведомлений")
        
        return True
    
    def generate_n8n_telegram_nodes(self):
        """Генерация конфигурации нодов Telegram для N8N"""
        templates = self.config['telegram_bot_config']['notification_templates']
        
        telegram_nodes = []
        
        for template_name, template_text in templates.items():
            node = {
                "parameters": {
                    "chatId": self.config['telegram_bot_config']['channel_id'],
                    "text": template_text,
                    "additionalFields": {
                        "parse_mode": "HTML",
                        "disable_web_page_preview": False
                    }
                },
                "id": f"telegram-{template_name.replace('_', '-')}",
                "name": f"Telegram {template_name.replace('_', ' ').title()}",
                "type": "n8n-nodes-base.telegram",
                "typeVersion": 1.2,
                "position": [2640, 300],
                "credentials": {
                    "telegramApi": {
                        "id": "telegram-godly-bot",
                        "name": "Telegram GodlySharing Bot"
                    }
                }
            }
            telegram_nodes.append(node)
        
        # Сохраняем конфигурацию нодов
        with open('n8n_telegram_nodes.json', 'w', encoding='utf-8') as f:
            json.dump(telegram_nodes, f, indent=2, ensure_ascii=False)
        
        print("📝 Конфигурация Telegram нодов сохранена в n8n_telegram_nodes.json")
        return telegram_nodes

def main():
    print("🚀 Настройка Telegram Bot для @GodlySharing")
    print("=" * 50)
    
    # Инициализация
    bot_setup = TelegramBotSetup()
    
    # Тестирование подключения
    if not bot_setup.test_bot_connection():
        return False
    
    # Настройка webhooks
    bot_setup.setup_webhook_handlers()
    
    # Генерация N8N нодов
    bot_setup.generate_n8n_telegram_nodes()
    
    # Отправка тестового сообщения
    if bot_setup.send_test_message():
        print("\n✅ Настройка завершена успешно!")
        print("🎯 Telegram Bot готов к работе с @GodlySharing")
        print("🔄 Теперь можно запускать N8N workflow")
        return True
    else:
        print("\n❌ Ошибка при настройке")
        return False

if __name__ == "__main__":
    main()
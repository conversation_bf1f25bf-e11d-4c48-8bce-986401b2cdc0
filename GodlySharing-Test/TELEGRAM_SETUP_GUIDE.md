# 🤖 Telegram Bot Setup Guide для @GodlySharing

## 📋 Обзор
Этот гайд поможет настроить Telegram бота для автоматических уведомлений о создании видео через MLX VideoLingo.

## 🔑 YouTube API Key
**Уже настроен**: `AIzaSyAeI-U4Et6xrWx4IX5Hol14eZyJAk0UGfU`

## 🚀 Быстрая настройка

### 1. Создание Telegram бота
1. Найдите @BotFather в Telegram
2. Отправьте `/newbot`
3. Выберите имя: `GodlySharing VideoLingo Bot`
4. Выберите username: `@GodlySharingBot` (или похожий)
5. Скопируйте токен бота

### 2. Настройка переменных окружения
```bash
export TELEGRAM_BOT_TOKEN="your_bot_token_here"
```

### 3. Запуск настройки
```bash
cd VideoLingo-MLX
python3 setup_telegram_bot.py
```

## 📊 N8N Workflow Integration

### Текущие Workflows:
1. **VideoLingo MLX - Godly Sharing Auto Video Creator** (ID: `vguPbNaXd6deG9cl`)
2. **VideoLingo MLX - YouTube API Integration** (ID: `VmAnMIv43AOhfB8F`) ✅ Новый

### Добавленные функции:
- ✅ YouTube API ключ интегрирован
- ✅ Telegram уведомления настроены
- ✅ Автоматическая загрузка на YouTube
- ✅ Мониторинг ошибок

## 🔧 Конфигурация

### YouTube Settings:
- **API Key**: AIzaSyAeI-U4Et6xrWx4IX5Hol14eZyJAk0UGfU
- **Privacy**: Public
- **Category**: People & Blogs (22)
- **Default Tags**: philosophy, wisdom, nature, mindfulness, shorts

### Telegram Settings:
- **Channel**: @GodlySharing
- **Bot**: @GodlySharingBot
- **Notifications**: Success, Error, Daily Stats

## 📱 Уведомления

### 🎬 Успешная загрузка видео:
```
🎬 Новое видео успешно загружено на YouTube!

📹 Название: [Video Title]
🔗 Ссылка: https://youtu.be/[VIDEO_ID]

✨ Автоматически создано через MLX VideoLingo
🤖 Локальная обработка без внешних API
```

### ⚠️ Ошибка обработки:
```
⚠️ Ошибка при создании видео!

🔴 Процесс: [Error Stage]
❌ Ошибка: [Error Message]
⏰ Время: [Timestamp]

🔧 Требуется ручная проверка MLX VideoLingo
```

## 🔄 Автоматизация

### Расписание:
- **Частота**: Каждые 2 часа
- **Cron**: `0 */2 * * *`
- **Timezone**: UTC

### Процесс:
1. 🎯 Генерация концепции (MLX)
2. 🖼️ Создание изображений (MLX)
3. 🎵 Синтез голоса (MLX)
4. 🎬 Рендеринг видео (FFmpeg)
5. 📝 Добавление субтитров
6. 📤 Загрузка на YouTube
7. 📱 Уведомление в Telegram

## 🛠️ Troubleshooting

### Проблемы с YouTube API:
- Проверьте квоты API
- Убедитесь что ключ активен
- Проверьте права доступа

### Проблемы с Telegram:
- Проверьте токен бота
- Убедитесь что бот добавлен в канал
- Проверьте права администратора

### Проблемы с MLX:
- Убедитесь что MLX Engine запущен: `http://localhost:8000`
- Проверьте логи: `docker logs mlx-video-engine`

## 📈 Мониторинг

### Логи N8N:
- Executions: http://localhost:5678/executions
- Workflows: http://localhost:5678/workflows

### MLX Engine Status:
- Health Check: http://localhost:8000/health
- Metrics: http://localhost:8000/metrics

## 🎯 Следующие шаги

1. ✅ YouTube API настроен
2. 🔄 Настроить Telegram бота
3. 🧪 Протестировать workflow
4. 📊 Настроить мониторинг
5. 🚀 Запустить автоматизацию

---
*Создано для @GodlySharing YouTube канала*
*MLX VideoLingo - 100% локальная обработка*
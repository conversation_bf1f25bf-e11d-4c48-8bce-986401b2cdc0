#!/usr/bin/env python3
"""
YouTube Channel Analyzer for @GodlySharing
Интеграция с MLX и Whisper AI
"""

import os
import json
import requests
from pathlib import Path
from typing import Dict, List, Optional
import yt_dlp
import whisper
from datetime import datetime
import logging

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class YouTubeAnalyzer:
    def __init__(self, api_key: str = None):
        """Инициализация анализатора YouTube канала"""
        self.api_key = api_key or os.getenv('YOUTUBE_API_KEY')
        self.base_url = "https://www.googleapis.com/youtube/v3"
        self.results_folder = Path("analysis_data")
        self.audio_folder = self.results_folder / "audio"
        self.transcripts_folder = self.results_folder / "transcripts"
        
        # Создаем папки если их нет
        self.results_folder.mkdir(exist_ok=True)
        self.audio_folder.mkdir(exist_ok=True)
        self.transcripts_folder.mkdir(exist_ok=True)
        
        # Инициализация Whisper
        try:
            self.whisper_model = whisper.load_model("base")
            logger.info("✅ Whisper модель загружена")
        except Exception as e:
            logger.error(f"❌ Ошибка загрузки Whisper: {e}")
            self.whisper_model = None

    def get_channel_videos(self, channel_id: str, max_videos: int = 20) -> List[Dict]:
        """Получение списка видео с канала"""
        try:
            url = f"{self.base_url}/search"
            params = {
                'key': self.api_key,
                'channelId': channel_id,
                'part': 'snippet',
                'order': 'date',
                'type': 'video',
                'maxResults': max_videos
            }
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            videos = []
            for item in data.get('items', []):
                videos.append({
                    'video_id': item['id']['videoId'],
                    'title': item['snippet']['title'],
                    'description': item['snippet']['description'],
                    'published_at': item['snippet']['publishedAt'],
                    'thumbnail': item['snippet']['thumbnails']['default']['url']
                })
            
            logger.info(f"✅ Найдено {len(videos)} видео")
            return videos
            
        except Exception as e:
            logger.error(f"❌ Ошибка получения видео: {e}")
            return []

    def get_video_stats(self, video_id: str) -> Dict:
        """Получение статистики видео"""
        try:
            url = f"{self.base_url}/videos"
            params = {
                'key': self.api_key,
                'id': video_id,
                'part': 'statistics,snippet'
            }
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if not data.get('items'):
                return {}
                
            item = data['items'][0]
            stats = item.get('statistics', {})
            snippet = item.get('snippet', {})
            
            # КРИТИЧЕСКИЙ ФИКС: YouTube API возвращает строки, а не числа!
            return {
                'view_count': int(stats.get('viewCount', '0')),
                'like_count': int(stats.get('likeCount', '0')),
                'comment_count': int(stats.get('commentCount', '0')),
                'duration': snippet.get('duration', ''),
                'tags': snippet.get('tags', [])
            }
            
        except Exception as e:
            logger.error(f"❌ Ошибка получения статистики: {e}")
            return {
                'view_count': 0,
                'like_count': 0,
                'comment_count': 0,
                'duration': '',
                'tags': []
            }

    def download_audio(self, video_id: str, title: str) -> Optional[str]:
        """Скачивание аудио из видео"""
        try:
            # Безопасное имя файла
            safe_filename = "".join(c for c in title[:50] if c.isalnum() or c in (' ', '-', '_')).strip()
            audio_path = self.audio_folder / f"{video_id}_{safe_filename}.mp3"
            
            ydl_opts = {
                'format': 'bestaudio/best',
                'outtmpl': str(audio_path.with_suffix('.%(ext)s')),
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'mp3',
                    'preferredquality': '192',
                }],
                'quiet': True,
                'no_warnings': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([f'https://www.youtube.com/watch?v={video_id}'])
            
            # Проверяем что файл создался
            if audio_path.exists():
                logger.info(f"✅ Аудио скачано: {audio_path.name}")
                return str(audio_path)
            else:
                logger.error(f"❌ Файл не найден: {audio_path}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Ошибка скачивания аудио: {e}")
            return None

    def transcribe_audio(self, audio_path: str, video_id: str) -> Optional[str]:
        """Транскрипция аудио с помощью Whisper"""
        try:
            if not self.whisper_model:
                logger.error("❌ Whisper модель не загружена")
                return None
                
            logger.info(f"🎙️ Начало транскрипции: {video_id}")
            result = self.whisper_model.transcribe(audio_path)
            
            transcript_data = {
                'video_id': video_id,
                'text': result['text'],
                'segments': result['segments'],
                'language': result['language'],
                'created_at': datetime.now().isoformat()
            }
            
            # Сохраняем транскрипт
            transcript_path = self.transcripts_folder / f"{video_id}.json"
            with open(transcript_path, 'w', encoding='utf-8') as f:
                json.dump(transcript_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ Транскрипт сохранен: {transcript_path.name}")
            return result['text']
            
        except Exception as e:
            logger.error(f"❌ Ошибка транскрипции: {e}")
            return None

    def analyze_channel(self, channel_id: str, max_videos: int = 20) -> Dict:
        """Полный анализ канала"""
        logger.info(f"🚀 Начало анализа канала: {channel_id}")
        
        # Получаем видео
        videos = self.get_channel_videos(channel_id, max_videos)
        if not videos:
            return {'error': 'Не удалось получить видео'}
        
        results = {
            'channel_id': channel_id,
            'analyzed_at': datetime.now().isoformat(),
            'total_videos': len(videos),
            'videos': [],
            'summary': {
                'total_views': 0,
                'total_likes': 0,
                'total_comments': 0,
                'avg_views': 0,
                'avg_likes': 0
            }
        }
        
        for i, video in enumerate(videos):
            logger.info(f"📊 Анализ видео {i+1}/{len(videos)}: {video['title'][:50]}...")
            
            # Получаем статистику
            stats = self.get_video_stats(video['video_id'])
            
            # Скачиваем аудио
            audio_path = self.download_audio(video['video_id'], video['title'])
            
            # Транскрипция
            transcript = None
            if audio_path:
                transcript = self.transcribe_audio(audio_path, video['video_id'])
            
            video_result = {
                **video,
                **stats,
                'transcript': transcript,
                'audio_file': audio_path
            }
            
            results['videos'].append(video_result)
            
            # Обновляем сводку
            results['summary']['total_views'] += stats.get('view_count', 0)
            results['summary']['total_likes'] += stats.get('like_count', 0)
            results['summary']['total_comments'] += stats.get('comment_count', 0)
        
        # Вычисляем средние значения
        if len(videos) > 0:
            results['summary']['avg_views'] = results['summary']['total_views'] // len(videos)
            results['summary']['avg_likes'] = results['summary']['total_likes'] // len(videos)
        
        # Сохраняем результаты
        results_path = self.results_folder / "analysis_report.json"
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ Анализ завершен: {results_path}")
        return results

# Для совместимости с analyzer_api.py
def analyze_godly_channel(max_videos: int = 20) -> Dict:
    """Анализ канала @GodlySharing"""
    analyzer = YouTubeAnalyzer()
    return analyzer.analyze_channel("UCl4-WBRqWA2MlxmZorKOV7w", max_videos)

if __name__ == "__main__":
    # Тестовый запуск
    result = analyze_godly_channel(3)
    print(json.dumps(result, ensure_ascii=False, indent=2))
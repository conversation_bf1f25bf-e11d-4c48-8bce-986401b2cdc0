#!/bin/bash

# Простой тест HTTPS через Tailscale
echo "🔐 Тест HTTPS через Tailscale"
echo "=========================="

# Проверяем Tailscale
if ! command -v tailscale &> /dev/null; then
    echo "❌ Tailscale не установлен"
    exit 1
fi

if ! tailscale status &> /dev/null; then
    echo "❌ Tailscale не подключен"
    echo "💡 Выполните: tailscale up"
    exit 1
fi

TAILSCALE_IP=$(tailscale ip -4)
echo "✅ Tailscale подключен: $TAILSCALE_IP"

# Проверяем Python
if ! python3 -c "import fastapi, uvicorn" 2>/dev/null; then
    echo "❌ FastAPI не установлен"
    echo "💡 Установка FastAPI..."
    python3 -m pip install fastapi uvicorn --user
    
    if ! python3 -c "import fastapi, uvicorn" 2>/dev/null; then
        echo "❌ Ошибка установки FastAPI"
        exit 1
    fi
fi

echo "✅ FastAPI установлен"

# Останавливаем существующие процессы
pkill -f "test_tailscale_simple.py" 2>/dev/null
pkill -f "tailscale serve" 2>/dev/null
sleep 2

# Запускаем тестовый сервер
echo "🚀 Запуск тестового сервера..."
python3 test_tailscale_simple.py &
SERVER_PID=$!

sleep 3

# Проверяем сервер
if ! curl -s http://localhost:8080/test > /dev/null; then
    echo "❌ Ошибка запуска сервера"
    kill $SERVER_PID 2>/dev/null
    exit 1
fi

echo "✅ Тестовый сервер запущен"

# Настраиваем Tailscale Serve
echo "🔗 Настройка Tailscale Serve..."
tailscale serve https:443 http://localhost:8080 &
SERVE_PID=$!

sleep 2

echo ""
echo "🎉 Тест успешно запущен!"
echo ""
echo "🌐 Тестовые адреса:"
echo "   https://ikas-macbook-pro.tailf48d3.ts.net"
echo "   https://ikas-macbook-pro.tailf48d3.ts.net/test"
echo "   https://************"
echo ""
echo "💡 Откройте в браузере для проверки HTTPS"
echo "💡 Нажмите Ctrl+C для остановки"

# Функция остановки
cleanup() {
    echo ""
    echo "🛑 Остановка тестовых сервисов..."
    kill $SERVER_PID 2>/dev/null
    kill $SERVE_PID 2>/dev/null
    tailscale serve reset 2>/dev/null
    echo "✅ Тест завершен"
    exit 0
}

trap cleanup SIGINT SIGTERM

# Мониторинг
while true; do
    if ! kill -0 $SERVER_PID 2>/dev/null; then
        echo "❌ Сервер остановился"
        cleanup
    fi
    sleep 5
done 
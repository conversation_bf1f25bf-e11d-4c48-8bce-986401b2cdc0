# 🎬 GodlySharing YouTube Analyzer

> **Продвинутый анализатор YouTube канала с AI транскрипцией и real-time мониторингом**

## 🚀 Возможности

- 📊 **Real-time анализ** - Двойные прогресс-бары и динамические результаты
- 🎙️ **AI Транскрипция** - Whisper AI для анализа контента  
- 🔐 **Безопасный доступ** - Tailscale HTTPS без открытых портов
- 📱 **Telegram интеграция** - Уведомления о завершении анализа
- 📁 **Файловый менеджер** - Автоматическое сохранение аудио файлов

## 🛠️ Технологии

- **Backend**: FastAPI + Python 3.9+
- **AI**: OpenAI Whisper для транскрипции
- **API**: YouTube Data API v3
- **UI**: Embedded HTML/CSS/JavaScript
- **Безопасность**: Tailscale Serve
- **Уведомления**: Telegram Bot API

## 📋 Установка

```bash
# Зависимости
pip install fastapi uvicorn openai-whisper yt-dlp google-api-python-client

# Запуск
python3 analyzer_api_tailscale.py

# Доступ через Tailscale
https://ikas-macbook-pro.tailf48d3.ts.net
```

## 🔧 Конфигурация

1. **YouTube API**: Настроить ключ в коде
2. **Telegram Bot**: Создать `telegram_config.json`
3. **Tailscale**: Настроить домен

## 📁 Структура проекта

```
VideoLingo-MLX/
├── analyzer_api_tailscale.py    # Основной сервер
├── youtube_analyzer.py          # Логика анализа
├── analysis_data/              # Результаты анализа
├── files_youtube_analyse/      # Сохраненные файлы
└── ika.txt                     # Отладочная информация
```

## 🚦 Git Workflow

- **main** - Стабильная версия для продакшена
- **dev** - Разработка и тестирование новых функций

## 🎯 Разработчики

- **Ika** - Архитектор и Product Owner
- **Claude (C)** - AI Assistant и Developer

## 📊 Статистика

- ✅ Анализ до 20 видео за сессию
- ✅ Транскрипция 3 видео с Whisper AI  
- ✅ Real-time прогресс мониторинг
- ✅ Безопасная HTTPS архитектура
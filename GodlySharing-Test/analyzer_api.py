#!/usr/bin/env python3
"""
FastAPI сервер для анализа канала @GodlySharing
Интеграция с N8N workflow
"""

from fastapi import FastAPI, BackgroundTasks, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import json
import asyncio
from datetime import datetime
from pathlib import Path
import subprocess
import sys

app = FastAPI(title="GodlySharing Channel Analyzer API", version="1.0.0")

# Добавляем CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # В продакшене лучше указать конкретные домены
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class AnalysisRequest(BaseModel):
    max_videos: int = 20
    include_transcripts: bool = True
    notify_telegram: bool = True

class AnalysisStatus(BaseModel):
    status: str
    started_at: Optional[str] = None
    videos_processed: int = 0
    current_video: str = ""
    estimated_completion: str = ""

# Глобальное состояние анализа
analysis_state = {
    "status": "idle",  # idle, running, completed, error
    "started_at": None,
    "videos_processed": 0,
    "current_video": "",
    "results": None,
    "error": None
}

@app.get("/")
async def root():
    return {
        "service": "GodlySharing Channel Analyzer",
        "version": "1.0.0",
        "status": "active",
        "endpoints": {
            "analyze": "/analyze-channel",
            "status": "/analysis-status",
            "results": "/analysis-results"
        }
    }

@app.post("/analyze-channel")
async def start_analysis(request: AnalysisRequest, background_tasks: BackgroundTasks):
    """Запуск анализа канала в фоновом режиме"""
    
    if analysis_state["status"] == "running":
        raise HTTPException(status_code=409, detail="Анализ уже выполняется")
    
    # Сбрасываем состояние
    analysis_state.update({
        "status": "running",
        "started_at": datetime.now().isoformat(),
        "videos_processed": 0,
        "current_video": "Подготовка...",
        "results": None,
        "error": None
    })
    
    # Запускаем анализ в фоне
    background_tasks.add_task(run_channel_analysis, request)
    
    return {
        "message": "Анализ канала @GodlySharing запущен",
        "status": "running",
        "estimated_time": f"{request.max_videos * 2} минут",
        "check_status": "/analysis-status"
    }

@app.get("/analysis-status")
async def get_analysis_status():
    """Получение текущего статуса анализа"""
    return AnalysisStatus(**analysis_state)

@app.get("/analysis-results")
async def get_analysis_results():
    """Получение результатов анализа"""
    
    if analysis_state["status"] != "completed":
        raise HTTPException(status_code=404, detail="Анализ не завершен")
    
    # Читаем результаты из файла
    results_file = Path("analysis_data/analysis_report.json")
    
    if not results_file.exists():
        raise HTTPException(status_code=404, detail="Файл результатов не найден")
    
    try:
        with open(results_file, 'r', encoding='utf-8') as f:
            results = json.load(f)
        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Ошибка чтения результатов: {e}")

@app.get("/analysis-report")
async def get_readable_report():
    """Получение читаемого отчета"""
    
    report_file = Path("analysis_data/ANALYSIS_REPORT.md")
    
    if not report_file.exists():
        raise HTTPException(status_code=404, detail="Отчет не найден")
    
    try:
        with open(report_file, 'r', encoding='utf-8') as f:
            content = f.read()
        return {"report": content}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Ошибка чтения отчета: {e}")

async def run_channel_analysis(request: AnalysisRequest):
    """Фоновая задача для анализа канала"""
    
    try:
        print(f"🚀 Запуск анализа канала (макс. {request.max_videos} видео)")
        
        # Импортируем анализатор
        from youtube_analyzer import GodlySharingAnalyzer
        
        analyzer = GodlySharingAnalyzer()
        
        # Обновляем статус
        analysis_state["current_video"] = "Поиск канала..."
        
        # 1. Получаем ID канала
        channel_id = analyzer.get_channel_id()
        if not channel_id:
            raise Exception("Канал @GodlySharing не найден")
        
        # 2. Получаем видео
        analysis_state["current_video"] = "Получение списка видео..."
        videos = analyzer.get_all_videos(channel_id, max_results=request.max_videos)
        
        if not videos:
            raise Exception("Видео не найдены")
        
        # 3. Получаем статистику
        analysis_state["current_video"] = "Получение статистики..."
        video_ids = [video['id']['videoId'] for video in videos]
        video_stats = analyzer.get_video_stats(video_ids)
        
        # Объединяем данные
        videos_with_stats = []
        for video, stats in zip(videos, video_stats):
            combined = {**video, **stats}
            videos_with_stats.append(combined)
        
        # 4. Транскрибация (если запрошена)
        videos_with_transcripts = []
        
        if request.include_transcripts:
            print("🎤 Начинаем транскрибацию...")
            
            # Ограничиваем транскрибацию первыми 10 видео для скорости
            transcribe_count = min(10, len(videos_with_stats))
            
            for i, video in enumerate(videos_with_stats[:transcribe_count], 1):
                video_id = video['id']
                title = video.get('snippet', {}).get('title', 'Без названия')
                
                analysis_state["current_video"] = f"Транскрибация {i}/{transcribe_count}: {title[:30]}..."
                analysis_state["videos_processed"] = i
                
                # Скачиваем аудио
                audio_file = analyzer.download_audio(video_id, title)
                
                if audio_file:
                    # Создаем транскрипцию
                    transcript = analyzer.transcribe_audio(audio_file, video_id)
                    video['transcript'] = transcript
                
                videos_with_transcripts.append(video)
            
            # Добавляем остальные видео без транскрипции
            videos_with_transcripts.extend(videos_with_stats[transcribe_count:])
        else:
            videos_with_transcripts = videos_with_stats
        
        # 5. Анализируем паттерны
        analysis_state["current_video"] = "Анализ паттернов контента..."
        analysis = analyzer.analyze_content_patterns(videos_with_transcripts)
        
        # 6. Сохраняем отчет
        analysis_state["current_video"] = "Сохранение отчета..."
        analyzer.save_analysis_report(analysis, videos_with_transcripts)
        
        # Завершаем успешно
        analysis_state.update({
            "status": "completed",
            "current_video": "Анализ завершен",
            "videos_processed": len(videos_with_transcripts),
            "results": {
                "total_videos": len(videos_with_transcripts),
                "transcribed_videos": transcribe_count if request.include_transcripts else 0,
                "avg_views": analysis['avg_views'],
                "avg_likes": analysis['avg_likes'],
                "report_file": "analysis_data/ANALYSIS_REPORT.md"
            }
        })
        
        # Отправляем уведомление в Telegram (если запрошено)
        if request.notify_telegram:
            await send_telegram_notification(analysis_state["results"])
        
        print("✅ Анализ канала завершен успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка анализа: {e}")
        analysis_state.update({
            "status": "error",
            "current_video": "Ошибка",
            "error": str(e)
        })

async def send_telegram_notification(results):
    """Отправка уведомления в Telegram"""
    try:
        import requests
        
        bot_token = "8033904583:AAFs60D_ddhgHoBctaQxKoMxW1qvN4XhQHM"
        chat_id = "-1001957777708"
        
        message = f"""🎯 Анализ канала @GodlySharing завершен!

📊 Результаты:
• Видео проанализировано: {results['total_videos']}
• Видео с транскрипцией: {results['transcribed_videos']}
• Средние просмотры: {results['avg_views']:,}
• Средние лайки: {results['avg_likes']:,}

📝 Отчет готов: {results['report_file']}

🚀 Готов к созданию нового контента на основе анализа!"""
        
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        data = {
            "chat_id": chat_id,
            "text": message,
            "parse_mode": "HTML"
        }
        
        response = requests.post(url, data=data)
        if response.status_code == 200:
            print("✅ Уведомление отправлено в Telegram")
        else:
            print(f"❌ Ошибка отправки в Telegram: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Ошибка Telegram уведомления: {e}")

if __name__ == "__main__":
    import uvicorn
    
    print("🚀 Запуск GodlySharing Channel Analyzer API")
    print("📊 Доступен на: http://localhost:8001")
    print("📖 Документация: http://localhost:8001/docs")
    
    uvicorn.run(app, host="0.0.0.0", port=8001)
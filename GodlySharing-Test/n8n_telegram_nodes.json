[{"parameters": {"chatId": "@GodlySharing", "text": "🎬 Новое видео успешно загружено на YouTube!\n\n📹 Название: {video_title}\n🔗 Ссылка: https://youtu.be/{video_id}\n\n✨ Автоматически создано через MLX VideoLingo\n🤖 Локальная обработка без внешних API\n📊 Канал: @GodlySharing\n\n#VideoLingo #MLX #AutomatedContent", "additionalFields": {"parse_mode": "HTML", "disable_web_page_preview": false}}, "id": "telegram-video-success", "name": "Telegram Video Success", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [2640, 300], "credentials": {"telegramApi": {"id": "telegram-godly-bot", "name": "Telegram GodlySharing Bot"}}}, {"parameters": {"chatId": "@GodlySharing", "text": "⚠️ Ошибка при создании видео!\n\n🔴 Процесс: {error_stage}\n❌ Ошибка: {error_message}\n⏰ Время: {timestamp}\n\n🔧 Требуется ручная проверка MLX VideoLingo\n🤖 @GodlySharingBot", "additionalFields": {"parse_mode": "HTML", "disable_web_page_preview": false}}, "id": "telegram-video-error", "name": "Telegram Video Error", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [2640, 300], "credentials": {"telegramApi": {"id": "telegram-godly-bot", "name": "Telegram GodlySharing Bot"}}}, {"parameters": {"chatId": "@GodlySharing", "text": "🚀 Начинаю создание нового видео...\n\n💡 Тема: {theme}\n🎨 Стиль: {style}\n⏱️ Время запуска: {timestamp}\n\n🤖 MLX VideoLingo работает...", "additionalFields": {"parse_mode": "HTML", "disable_web_page_preview": false}}, "id": "telegram-process-start", "name": "Telegram Process Start", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [2640, 300], "credentials": {"telegramApi": {"id": "telegram-godly-bot", "name": "Telegram GodlySharing Bot"}}}, {"parameters": {"chatId": "@GodlySharing", "text": "📊 Статистика за сегодня:\n\n✅ Видео создано: {videos_created}\n📈 Просмотры: {total_views}\n👥 Новые подписчики: {new_subscribers}\n⚡ Время обработки: {avg_processing_time}мин\n\n🎯 @GodlySharing растет!", "additionalFields": {"parse_mode": "HTML", "disable_web_page_preview": false}}, "id": "telegram-daily-stats", "name": "Telegram Daily Stats", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [2640, 300], "credentials": {"telegramApi": {"id": "telegram-godly-bot", "name": "Telegram GodlySharing Bot"}}}]
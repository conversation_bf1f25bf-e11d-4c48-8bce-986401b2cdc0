#!/usr/bin/env python3
"""
Простой HTTPS сервер для analyzer_dashboard.html
"""

import http.server
import ssl
import socketserver
import os

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

if __name__ == "__main__":
    PORT = 8444
    
    # Переходим в рабочую директорию
    os.chdir('/Users/<USER>/Projects/MCP-SuperAssistant/VideoLingo-MLX')
    
    with socketserver.TCPServer(("", PORT), CORSHTTPRequestHandler) as httpd:
        print(f"🔒 HTTPS сервер запущен на https://localhost:{PORT}")
        print(f"📄 Откройте: https://localhost:{PORT}/analyzer_dashboard.html")
        
        # Настройка SSL
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        context.load_cert_chain('cert.pem', 'key.pem')
        
        httpd.socket = context.wrap_socket(httpd.socket, server_side=True)
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n⏹️ Сервер остановлен")
            httpd.shutdown()
{"name": "VideoLingo MLX - YouTube & Telegram Integration", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 */2 * * *"}]}}, "id": "schedule-trigger", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [240, 300]}, {"parameters": {"assignments": {"assignments": [{"id": "starting_idea", "name": "starting_idea", "value": "Wisdom through nature - daily philosophy for mindful living", "type": "string"}, {"id": "youtube_api_key", "name": "youtube_api_key", "value": "AIzaSyAeI-U4Et6xrWx4IX5Hol14eZyJAk0UGfU", "type": "string"}]}}, "id": "input-config", "name": "Configuration", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [440, 300]}]}
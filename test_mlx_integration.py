#!/usr/bin/env python3
"""
🔥 MLX-Whisper Integration Test for VideoLingo
Тест интеграции MLX-Whisper в VideoLingo

Мастер Ика + Claude - Команда Воинов! 🚀
"""

import os
import sys
import time
import warnings
warnings.filterwarnings("ignore")

# Add VideoLingo to path
sys.path.append('/Volumes/SAB500/VideoLingo-MLX-Project/VideoLingo')

def test_mlx_imports():
    """Test MLX framework imports"""
    print("🔍 Testing MLX framework imports...")
    
    try:
        import mlx_whisper
        print(f"✅ MLX-Whisper: {mlx_whisper.__version__}")
    except Exception as e:
        print(f"❌ MLX-Whisper import failed: {e}")
        return False
    
    try:
        import lightning_whisper_mlx
        print("✅ Lightning-Whisper-MLX: Ready")
    except Exception as e:
        print(f"❌ Lightning-Whisper-MLX import failed: {e}")
        return False
    
    try:
        import mlx.core as mx
        print("✅ MLX Core: Ready")
    except Exception as e:
        print(f"❌ MLX Core import failed: {e}")
        return False
    
    return True

def test_videolingo_config():
    """Test VideoLingo configuration for MLX"""
    print("\n🔧 Testing VideoLingo MLX configuration...")
    
    try:
        from core.utils import load_key, update_key
        
        # Test config loading
        whisper_runtime = load_key("whisper.runtime")
        whisper_model = load_key("whisper.model") 
        batch_size = load_key("whisper.batch_size", default=12)
        
        print(f"✅ Whisper Runtime: {whisper_runtime}")
        print(f"✅ Whisper Model: {whisper_model}")
        print(f"✅ Batch Size: {batch_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False

def test_mlx_whisper_module():
    """Test MLX-Whisper module import from VideoLingo"""
    print("\n🎤 Testing MLX-Whisper VideoLingo module...")
    
    try:
        from core.asr_backend.mlx_whisper_local import transcribe_audio, get_model_info
        
        # Test model info
        info = get_model_info()
        print(f"✅ MLX Module Info: {info}")
        
        print("✅ MLX-Whisper module loaded successfully!")
        return True
        
    except Exception as e:
        print(f"❌ MLX-Whisper module test failed: {e}")
        return False

def test_asr_loader():
    """Test ASR loader MLX integration"""
    print("\n🔄 Testing ASR loader MLX integration...")
    
    try:
        # Test that _2_asr.py can handle mlx_local runtime
        from core.utils import update_key, load_key
        
        # Temporarily set MLX runtime
        original_runtime = load_key("whisper.runtime")
        update_key("whisper.runtime", "mlx_local")
        
        # Import ASR module
        import core._2_asr as asr_module
        print("✅ ASR module import successful with MLX runtime")
        
        # Restore original runtime
        update_key("whisper.runtime", original_runtime)
        
        return True
        
    except Exception as e:
        print(f"❌ ASR loader test failed: {e}")
        return False

def test_streamlit_ui():
    """Test Streamlit UI MLX options"""
    print("\n🖥️  Testing Streamlit UI MLX integration...")
    
    try:
        from core.st_utils.sidebar_setting import page_setting
        print("✅ Streamlit UI module loaded successfully")
        print("✅ MLX runtime option should be available in UI")
        return True
        
    except Exception as e:
        print(f"❌ Streamlit UI test failed: {e}")
        return False

def run_integration_test():
    """Run complete MLX-Whisper integration test"""
    print("🎬 MLX-Whisper VideoLingo Integration Test")
    print("=" * 50)
    
    tests = [
        ("MLX Framework Imports", test_mlx_imports),
        ("VideoLingo Configuration", test_videolingo_config),
        ("MLX-Whisper Module", test_mlx_whisper_module),
        ("ASR Loader Integration", test_asr_loader),
        ("Streamlit UI Integration", test_streamlit_ui),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        print("-" * 30)
        
        start_time = time.time()
        result = test_func()
        end_time = time.time()
        
        results.append((test_name, result, end_time - start_time))
        
        if result:
            print(f"✅ {test_name}: PASSED ({end_time - start_time:.2f}s)")
        else:
            print(f"❌ {test_name}: FAILED ({end_time - start_time:.2f}s)")
    
    # Summary
    print("\n" + "=" * 50)
    print("🏆 MLX-Whisper Integration Test Results")
    print("=" * 50)
    
    passed = sum(1 for _, result, _ in results if result)
    total = len(results)
    
    for test_name, result, duration in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}: {duration:.2f}s")
    
    print("-" * 50)
    print(f"📊 Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! MLX-Whisper integration is ready!")
        print("🚀 VideoLingo + MLX готов к боевому применению!")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = run_integration_test()
    sys.exit(0 if success else 1)

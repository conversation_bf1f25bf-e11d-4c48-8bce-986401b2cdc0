#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Загружаем переменные окружения из .env файла
function loadEnvFile(envPath) {
  if (!fs.existsSync(envPath)) {
    console.error(`Файл .env не найден: ${envPath}`);
    process.exit(1);
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envVars = {};
  
  envContent.split('\n').forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        envVars[key.trim()] = valueParts.join('=').trim();
      }
    }
  });
  
  return envVars;
}

// Подставляем переменные окружения в JSON
function substituteEnvVars(jsonContent, envVars) {
  let result = jsonContent;
  
  // Заменяем все вхождения ${VAR_NAME} на соответствующие значения
  Object.keys(envVars).forEach(key => {
    const placeholder = `\${${key}}`;
    const value = envVars[key];
    result = result.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), value);
  });
  
  return result;
}

function main() {
  const configPath = process.argv[2] || './mcpconfig.json';
  const envPath = process.argv[3] || './.env';
  const outputPath = process.argv[4] || './mcpconfig.resolved.json';
  
  console.log(`Загружаем конфигурацию из: ${configPath}`);
  console.log(`Загружаем переменные окружения из: ${envPath}`);
  console.log(`Сохраняем результат в: ${outputPath}`);
  
  // Загружаем переменные окружения
  const envVars = loadEnvFile(envPath);
  console.log(`Загружено ${Object.keys(envVars).length} переменных окружения`);
  
  // Читаем конфигурацию
  if (!fs.existsSync(configPath)) {
    console.error(`Файл конфигурации не найден: ${configPath}`);
    process.exit(1);
  }
  
  const configContent = fs.readFileSync(configPath, 'utf8');
  
  // Подставляем переменные
  const resolvedContent = substituteEnvVars(configContent, envVars);
  
  // Проверяем валидность JSON
  try {
    JSON.parse(resolvedContent);
    console.log('✅ JSON синтаксис корректен');
  } catch (error) {
    console.error('❌ Ошибка в JSON синтаксисе:', error.message);
    process.exit(1);
  }
  
  // Сохраняем результат
  fs.writeFileSync(outputPath, resolvedContent, 'utf8');
  console.log(`✅ Конфигурация с подставленными переменными сохранена в: ${outputPath}`);
}

if (require.main === module) {
  main();
}

module.exports = { loadEnvFile, substituteEnvVars };

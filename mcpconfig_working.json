{"mcpServers": {"memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {}}, "mcp-shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager"], "env": {"DATA_DIR": "/Users/<USER>/Projects/MCP_TOOLS/data", "TEMPLATES_USE": "en", "ENABLE_GUI": "false"}}, "youtube-advanced": {"command": "node", "args": ["/Users/<USER>/Projects/MCP_SERVS/youtube-advanced/index.js"], "env": {"YOUTUBE_API_KEY": "AIzaSyAeI-U4Et6xrWx4IX5Hol14eZyJAk0UGfU"}}}}
curl "http://localhost:8001/analyze-channel?channel_id=UCl4-WBRqWA2MlxmZorKOV7w&max_videos=3"
{"detail":"Method Not Allowed"}

cd /Users/<USER>/Projects/MCP-SuperAssistant/GodlySharing-Test && python3 analyzer_api.py
🚀 Запуск GodlySharing Channel Analyzer API
📊 Доступен на: http://localhost:8001
📖 Документация: http://localhost:8001/docs
INFO:     Started server process [33052]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
INFO:     127.0.0.1:50111 - "GET /analyze-channel?channel_id=UCl4-WBRqWA2MlxmZorKOV7w&max_videos=3 HTTP/1.1" 405 Method Not Allowed
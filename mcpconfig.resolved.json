{"mcpServers": {"mcp-installer": {"command": "npx", "args": ["@anaisbetts/mcp-installer"], "env": {}}, "context7-mcp": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@upstash/context7-mcp", "--key", "7122bec7-4656-4efe-a4a2-12921a055ec6"], "env": {}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}}, "whatsapp": {"command": "/usr/local/bin/uv", "args": ["--directory", "/Volumes/DATA/whatsapp-mcp/whatsapp-mcp-server", "run", "main.py"], "env": {}}, "desktop-commander": {"command": "npx", "args": ["@wonderwhy-er/desktop-commander@latest"], "env": {}}, "servers": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@jlia0/servers", "--key", "b523f26f-db45-4d27-bbda-225aa68a616e", "--profile", "salty-wildfowl-03J83F"], "env": {}}, "toolbox": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@smithery/toolbox", "--key", "7122bec7-4656-4efe-a4a2-12921a055ec6", "--profile", "renewed-whale-FrWczB"], "env": {}}, "mcp-shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager"], "env": {"DATA_DIR": "/Users/<USER>/Projects/MCP_TOOLS/data", "TEMPLATES_USE": "en", "ENABLE_GUI": "false", "MCP_PROMPT_PLAN_TASK_APPEND": "\n\n## MCP Tools Project Guidelines\n\n- Focus on MCP server development\n- Ensure compatibility with multiple clients\n- Document all tools and resources\n- Follow TypeScript best practices\n- Test with real MCP clients", "MCP_PROMPT_EXECUTE_TASK_APPEND": "\n\n## MCP Development Standards\n\n- Test with <PERSON> and other MCP clients\n- Validate all tool schemas\n- Ensure proper error handling\n- Update documentation\n- Consider cross-platform compatibility"}}, "ika-compressor-mcp": {"command": "npx", "args": ["-y", "/Users/<USER>/Projects/handmade/python-original/ika-compressor-mcp"], "env": {"PYTHON_PATH": "python3"}}, "youtube-analytics-stable": {"command": "node", "args": ["/Users/<USER>/Downloads/godly-sharing-automation/mcp-servers/youtube-analytics/index.js"], "env": {"YOUTUBE_API_KEY": "AIzaSyAeI-U4Et6xrWx4IX5Hol14eZyJAk0UGfU"}}, "youtube-advanced": {"command": "node", "args": ["/Users/<USER>/Projects/MCP_SERVS/youtube-advanced/index.js"], "env": {"YOUTUBE_API_KEY": "AIzaSyAeI-U4Et6xrWx4IX5Hol14eZyJAk0UGfU"}}, "n8n-mcp-server": {"command": "npx", "args": ["n8n-mcp-server"], "env": {"N8N_API_URL": "http://localhost:5678/api/v1", "N8N_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlNWNkYmI0MC1jZTc1LTQzNWMtOTgwZS1mNTA4YTMxZmMwNDIiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUwNTU4Nzc0fQ.1s29_KYwpF918F_fd01by9JVftgeFF-7DKUJdgPCAzU"}}, "mcp-ssh-server": {"command": "node", "args": ["/Users/<USER>/Projects/MCP_SERVS/mcp-ssh-server/stable-server.js"], "env": {}}, "timer-mcp-server": {"command": "node", "args": ["/Users/<USER>/Projects/MCP_SERVS/timer-mcp-server/dist/index.js"], "env": {}}, "browser-use-mcp-server": {"command": "/usr/local/bin/uv", "args": ["--directory", "/Users/<USER>/Projects/MCP_SERVS/browser-use-mcp-server", "run", "python", "-m", "browser_use_mcp_server"], "env": {}}, "git-server": {"command": "/usr/local/bin/uv", "args": ["--directory", "/Users/<USER>/Projects/MCP_SERVS/servers/src/git", "run", "python", "-m", "mcp_server_git"], "env": {"GITHUB_TOKEN": "*********************************************************************************************"}}, "fetch-server": {"command": "/usr/local/bin/uv", "args": ["--directory", "/Users/<USER>/Projects/MCP_SERVS/servers/src/fetch", "run", "python", "-m", "mcp_server_fetch"], "env": {}}, "godly-filesystem": {"command": "node", "args": ["/Users/<USER>/Projects/handmade/node-rewritten/filesystem-manager/index.js"], "env": {}}, "godly-ssl": {"command": "node", "args": ["/Users/<USER>/Projects/handmade/node-rewritten/ssl-manager/index.js"], "env": {}}, "godly-whatsapp": {"command": "node", "args": ["/Users/<USER>/Projects/handmade/node-rewritten/whatsapp-manager/index.js"], "env": {"TELEGRAM_BOT_TOKEN": "**********************************************"}}, "godly-timer": {"command": "node", "args": ["/Users/<USER>/Projects/handmade/node-rewritten/timer-manager/index.js"], "env": {"TELEGRAM_BOT_TOKEN": "**********************************************"}}, "videolingo-planner": {"command": "node", "args": ["/Users/<USER>/Projects/handmade/node-native/videolingo-planner/index.js"], "env": {}}, "gdrive-server": {"command": "npx", "args": ["tsx", "/Users/<USER>/Projects/MCP_SERVS/gdrive-server/index.ts"], "env": {}}, "postgres-server": {"command": "npx", "args": ["tsx", "/Users/<USER>/Projects/MCP_SERVS/postgres-server/index.ts"], "env": {}}, "puppeteer-server": {"command": "npx", "args": ["tsx", "/Users/<USER>/Projects/MCP_SERVS/puppeteer-server/index.ts"], "env": {}}, "slack-server": {"command": "npx", "args": ["tsx", "/Users/<USER>/Projects/MCP_SERVS/slack-server/index.ts"], "env": {}}, "sqlite-server": {"command": "npx", "args": ["tsx", "/Users/<USER>/Projects/MCP_SERVS/sqlite-server/index.ts"], "env": {}}}}
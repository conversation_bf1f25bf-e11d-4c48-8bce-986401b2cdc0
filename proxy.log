Initializing MCP SuperAssistant proxy...
Connecting to memory (stdio - inferred)...
Knowledge Graph MCP Server running on stdio
Connected to memory: 9 tools, 0 resources, 0 prompts
Connecting to mcp-shrimp-task-manager (stdio - inferred)...
Connected to mcp-shrimp-task-manager: 15 tools, 0 resources, 0 prompts
Connecting to youtube-advanced (stdio - inferred)...
Godly Sharing Advanced YouTube MCP running
Connected to youtube-advanced: 7 tools, 0 resources, 0 prompts
Successfully initialized mcpsuperassistantproxy with 3 servers
MCP SuperAssistant Proxy running with backwards compatibility at:
  - Modern Streamable HTTP: http://localhost:3006/mcp
  - Legacy SSE: http://localhost:3006/sse
  - Legacy messages: http://localhost:3006/messages

Clients should connect to http://localhost:3006/mcp for automatic backwards compatibility.

Received SIGTERM, shutting down MCP SuperAssistant proxy gracefully...
Starting graceful shutdown...
Disconnected from memory
Disconnected from mcp-shrimp-task-manager
Disconnected from youtube-advanced
HTTP server closed successfully
MCP SuperAssistant Proxy stopped
Graceful shutdown completed

{"a. Download or Upload Video": "a. 動画のダウンロードまたはアップロード", "Delete and Reselect": "削除して再選択", "Enter YouTube link:": "YouTubeリンクを入力:", "Resolution": "解像度", "Download Video": "動画をダウンロード", "Or upload video": "または動画をアップロード", "Youtube Settings": "Youtube 設定", "Cookies Path": "Cookieファイルのパス", "LLM Configuration": "LLM設定", "API_KEY": "APIキー", "BASE_URL": "ベースURL", "MODEL": "モデル", "Openai format, will add /v1/chat/completions automatically": "OpenAI形式、/v1/chat/completionsが自動的に追加されます", "click to check API validity": "クリックしてAPIの有効性を確認", "API Key is valid": "APIキーは有効です", "API Key is invalid": "APIキーが無効です", "Recog Lang": "認識言語", "Subtitles Settings": "字幕設定", "Target Lang": "目標言語", "Input any language in natural language, as long as llm can understand": "LLMが理解できる限り、自然言語で任意の言語を入力してください", "Vocal separation enhance": "音声分離強化", "Burn-in Subtitles": "字幕を焼き付け", "Whether to burn subtitles into the video, will increase processing time": "字幕を動画に焼き付けるかどうか、処理時間が増加します", "Video Resolution": "動画解像度", "Recommended for videos with loud background noise, but will increase processing time": "背景ノイズの大きい動画に推奨されますが、処理時間が増加します", "Dubbing Settings": "吹き替え設定", "TTS Method": "TTS方式", "SiliconFlow API Key": "SiliconFlow APIキー", "Mode Selection": "モード選択", "Preset": "プリセット", "Refer_stable": "安定参照", "Refer_dynamic": "動的参照", "OpenAI Voice": "OpenAI音声", "Fish TTS Character": "Fish TTSキャラクター", "Azure Voice": "Azure音声", "Please refer to Github homepage for GPT_SoVITS configuration": "GPT_SoVITSの設定についてはGithubホームページを参照してください", "SoVITS Character": "SoVITSキャラクター", "Refer Mode": "参照モード", "Mode 1: Use provided reference audio only": "モード1：提供された参照音声のみを使用", "Mode 2: Use first audio from video as reference": "モード2：動画の最初の音声を参照として使用", "Mode 3: Use each audio from video as reference": "モード3：動画の各音声を参照として使用", "Configure reference audio mode for GPT-SoVITS": "GPT-SoVITSの参照音声モードを設定", "Edge TTS Voice": "Edge TTS音声", "=====NOTE=====": "以下はst.pyの内容です", "b. Translate and Generate Subtitles": "b. 翻訳と字幕生成", "This stage includes the following steps:": "このステージには以下の手順が含まれます:", "WhisperX word-level transcription": "WhisperX単語レベル文字起こし", "Sentence segmentation using NLP and LLM": "NLPとLLMを使用した文章分割", "Summarization and multi-step translation": "要約と多段階翻訳", "Cutting and aligning long subtitles": "長い字幕の切断と整列", "Generating timeline and subtitles": "タイムラインと字幕の生成", "Merging subtitles into the video": "字幕を動画に統合", "Start Processing Subtitles": "字幕処理を開始", "Download All Srt Files": "すべてのSrtファイルをダウンロード", "Archive to 'history'": "'history'にアーカイブ", "Using Whisper for transcription...": "Whisperで文字起こしを実行中...", "Splitting long sentences...": "長文を分割中...", "Summarizing and translating...": "要約と翻訳中...", "Processing and aligning subtitles...": "字幕の処理と整列中...", "Merging subtitles to video...": "字幕を動画に統合中...", "⚠️ PAUSE_BEFORE_TRANSLATE. Go to `output/log/terminology.json` to edit terminology. Then press ENTER to continue...": "⚠️ 翻訳前に一時停止。`output/log/terminology.json`で用語を編集してください。その後、Enterキーを押して続行...", "Subtitle processing complete! 🎉": "字幕処理完了！🎉", "c. Dubbing": "c. 吹き替え", "Generate audio tasks and chunks": "音声タスクとチャンクの生成", "Extract reference audio": "参照音声の抽出", "Generate and merge audio files": "音声ファイルの生成と統合", "Merge final audio into video": "最終音声を動画に統合", "Start Audio Processing": "音声処理を開始", "Audio processing is complete! You can check the audio files in the `output` folder.": "音声処理が完了しました！`output`フォルダで音声ファイルを確認できます。", "Delete dubbing files": "吹き替えファイルを削除", "Generate audio tasks": "音声タスクを生成", "Extract refer audio": "参照音声を抽出", "Generate all audio": "すべての音声を生成", "Merge full audio": "完全な音声を統合", "Merge dubbing to the video": "吹き替えを動画に統合", "Audio processing complete! 🎇": "音声処理完了！🎇", "Hello, welcome to VideoLingo. If you encounter any issues, feel free to get instant answers with our Free QA Agent <a href=\"https://share.fastgpt.in/chat/share?shareId=066w11n3r9aq6879r4z0v9rh\" target=\"_blank\">here</a>! You can also try out our SaaS website at <a href=\"https://videolingo.io\" target=\"_blank\">videolingo.io</a> for free!": "VideoLingoへようこそ。問題が発生した場合は、無料のQAエージェント<a href=\"https://share.fastgpt.in/chat/share?shareId=066w11n3r9aq6879r4z0v9rh\" target=\"_blank\">こちら</a>で即座に回答を得ることができます！また、SaaSウェブサイト<a href=\"https://videolingo.io\" target=\"_blank\">videolingo.io</a>を無料でお試しいただけます！", "WhisperX Runtime": "WhisperX ランタイム", "Local runtime requires >8GB GPU, cloud runtime requires 302ai API key, elevenlabs runtime requires ElevenLabs API key": "ローカルランタイムは8GB以上のGPUが必要、クラウドランタイムは302ai APIキーが必要です、elevenlabsランタイムはElevenLabs APIキーが必要です", "WhisperX 302ai API": "WhisperX 302ai API", "=====NOTE2=====": "以下はinstall.pyの内容です", "🚀 Starting Installation": "🚀 インストールを開始", "Do you need to auto-configure PyPI mirrors? (Recommended if you have difficulty accessing pypi.org)": "PyPIミラーを自動設定しますか？（pypi.orgへのアクセスが困難な場合は推奨）", "🎮 NVIDIA GPU detected, installing CUDA version of PyTorch...": "🎮 NVIDIA GPUを検出、PyTorchのCUDAバージョンをインストール中...", "🍎 MacOS detected, installing CPU version of PyTorch... Note: it might be slow during whisperX transcription.": "🍎 MacOSを検出、PyTorchのCPUバージョンをインストール中... 注：whisperX文字起こし時に遅くなる可能性があります。", "💻 No NVIDIA GPU detected, installing CPU version of PyTorch... Note: it might be slow during whisperX transcription.": "💻 NVIDIA GPUが検出されません、PyTorchのCPUバージョンをインストール中... 注：whisperX文字起こし時に遅くなる可能性があります。", "❌ Failed to install requirements:": "❌ 要件のインストールに失敗:", "✅ FFmpeg is already installed": "✅ FFmpegはすでにインストールされています", "❌ FFmpeg not found\n\n": "❌ FFmpegが見つかりません\n\n", "🛠️ Install using:": "🛠️ インストール方法：", "💡 Note:": "💡 注意：", "🔄 After installing FFmpeg, please run this installer again:": "🔄 FFmpegをインストールした後、このインストーラーを再度実行してください：", "Install Chocolatey first (https://chocolatey.org/)": "最初にChocolateyをインストールしてください (https://chocolatey.org/)", "Install Homebrew first (https://brew.sh/)": "最初にHomebrewをインストールしてください (https://brew.sh/)", "Use your distribution's package manager": "お使いのディストリビューションのパッケージマネージャーを使用してください", "FFmpeg is required. Please install it and run the installer again.": "FFmpegが必要です。インストールして、インストーラーを再度実行してください。", "Installation completed": "インストール完了", "Now I will run this command to start the application:": "次のコマンドでアプリケーションを起動します:", "Note: First startup may take up to 1 minute": "注：初回起動には最大1分かかる場合があります", "If the application fails to start:": "アプリケーションが起動しない場合:", "Check your network connection": "ネットワーク接続を確認してください", "Re-run the installer: [bold]python install.py[/bold]": "インストーラーを再実行: [bold]python install.py[/bold]", "Installing requirements using `pip install -r requirements.txt`": "依存関係を `pip install -r requirements.txt` でインストール中", "Detected NVIDIA GPU(s)": "NVIDIA GPUを検出しました", "No NVIDIA GPU detected": "NVIDIA GPUが検出されません", "No NVIDIA GPU detected or NVIDIA drivers not properly installed": "NVIDIA GPUが検出されないか、NVIDIAドライバーが正しくインストールされていません", "LLM JSON Format Support": "LLM JSON形式サポート", "Enable if your LLM supports JSON mode output": "LLMがJSON出力モードをサポートしている場合に有効化"}
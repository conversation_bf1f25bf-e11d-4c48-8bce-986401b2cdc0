{"a. Download or Upload Video": "a. Download or Upload Video", "Delete and Reselect": "Delete and Reselect", "Enter YouTube link:": "Enter YouTube link:", "Resolution": "Resolution", "Download Video": "Download Video", "Or upload video": "Or upload video", "Youtube Settings": "Youtube Settings", "Cookies Path": "Cookies Path", "LLM Configuration": "LLM Configuration", "API_KEY": "API_KEY", "BASE_URL": "BASE_URL", "MODEL": "MODEL", "Openai format, will add /v1/chat/completions automatically": "Openai format, will add /v1/chat/completions automatically", "click to check API validity": "click to check API validity", "API Key is valid": "API Key is valid", "API Key is invalid": "API Key is invalid", "Recog Lang": "<PERSON><PERSON><PERSON>", "Subtitles Settings": "Subtitles Settings", "Target Lang": "Target Lang", "Input any language in natural language, as long as llm can understand": "Input any language in natural language, as long as llm can understand", "Vocal separation enhance": "Vocal separation enhance", "Burn-in Subtitles": "Burn-in Subtitles", "Whether to burn subtitles into the video, will increase processing time": "Whether to burn subtitles into the video, will increase processing time", "Video Resolution": "Video Resolution", "Recommended for videos with loud background noise, but will increase processing time": "Recommended for videos with loud background noise, but will increase processing time", "Dubbing Settings": "Dubbing Settings", "TTS Method": "TTS Method", "SiliconFlow API Key": "SiliconFlow API Key", "Mode Selection": "Mode Selection", "Preset": "Preset", "Refer_stable": "Refer_stable", "Refer_dynamic": "Refer_dynamic", "OpenAI Voice": "OpenAI Voice", "Fish TTS Character": "Fish TTS Character", "Azure Voice": "Azure Voice", "Please refer to Github homepage for GPT_SoVITS configuration": "Please refer to Github homepage for GPT_SoVITS configuration", "SoVITS Character": "SoVITS Character", "Refer Mode": "Refer Mode", "Mode 1: Use provided reference audio only": "Mode 1: Use provided reference audio only", "Mode 2: Use first audio from video as reference": "Mode 2: Use first audio from video as reference", "Mode 3: Use each audio from video as reference": "Mode 3: Use each audio from video as reference", "Configure reference audio mode for GPT-SoVITS": "Configure reference audio mode for GPT-SoVITS", "Edge TTS Voice": "Edge TTS Voice", "=====NOTE=====": "BELOW IS in st.py", "b. Translate and Generate Subtitles": "b. Translate and Generate Subtitles", "This stage includes the following steps:": "This stage includes the following steps:", "WhisperX word-level transcription": "WhisperX word-level transcription", "Sentence segmentation using NLP and LLM": "Sentence segmentation using NLP and LLM", "Summarization and multi-step translation": "Summarization and multi-step translation", "Cutting and aligning long subtitles": "Cutting and aligning long subtitles", "Generating timeline and subtitles": "Generating timeline and subtitles", "Merging subtitles into the video": "Merging subtitles into the video", "Start Processing Subtitles": "Start Processing Subtitles", "Download All Srt Files": "Download All Srt Files", "Archive to 'history'": "Archive to 'history'", "Using Whisper for transcription...": "Using Whisper for transcription...", "Splitting long sentences...": "Splitting long sentences...", "Summarizing and translating...": "Summarizing and translating...", "Processing and aligning subtitles...": "Processing and aligning subtitles...", "Merging subtitles to video...": "Merging subtitles to video...", "⚠️ PAUSE_BEFORE_TRANSLATE. Go to `output/log/terminology.json` to edit terminology. Then press ENTER to continue...": "⚠️ PAUSE_BEFORE_TRANSLATE. Go to `output/log/terminology.json` to edit terminology. Then press ENTER to continue...", "Subtitle processing complete! 🎉": "Subtitle processing complete! 🎉", "c. Dubbing": "c. <PERSON><PERSON>", "Generate audio tasks and chunks": "Generate audio tasks and chunks", "Extract reference audio": "Extract reference audio", "Generate and merge audio files": "Generate and merge audio files", "Merge final audio into video": "Merge final audio into video", "Start Audio Processing": "Start Audio Processing", "Audio processing is complete! You can check the audio files in the `output` folder.": "Audio processing is complete! You can check the audio files in the `output` folder.", "Delete dubbing files": "Delete dubbing files", "Generate audio tasks": "Generate audio tasks", "Extract refer audio": "Extract refer audio", "Generate all audio": "Generate all audio", "Merge full audio": "Merge full audio", "Merge dubbing to the video": "<PERSON><PERSON> dubbing to the video", "Audio processing complete! 🎇": "Audio processing complete! 🎇", "Hello, welcome to VideoLingo. If you encounter any issues, feel free to get instant answers with our Free QA Agent <a href=\"https://share.fastgpt.in/chat/share?shareId=066w11n3r9aq6879r4z0v9rh\" target=\"_blank\">here</a>! You can also try out our SaaS website at <a href=\"https://videolingo.io\" target=\"_blank\">videolingo.io</a> for free!": "Hello, welcome to VideoLingo. If you encounter any issues, feel free to get instant answers with our Free QA Agent <a href=\"https://share.fastgpt.in/chat/share?shareId=066w11n3r9aq6879r4z0v9rh\" target=\"_blank\">here</a>! You can also try out our SaaS website at <a href=\"https://videolingo.io\" target=\"_blank\">videolingo.io</a> for free!", "WhisperX Runtime": "WhisperX Runtime", "Local runtime requires >8GB GPU, cloud runtime requires 302ai API key, elevenlabs runtime requires ElevenLabs API key": "Local runtime requires >8GB GPU, cloud runtime requires 302ai API key, elevenlabs runtime requires ElevenLabs API key", "WhisperX 302ai API": "WhisperX 302ai API", "=====NOTE2=====": "BELOW IS in install.py", "🚀 Starting Installation": "🚀 Starting Installation", "Do you need to auto-configure PyPI mirrors? (Recommended if you have difficulty accessing pypi.org)": "Do you need to auto-configure PyPI mirrors? (Recommended if you have difficulty accessing pypi.org)", "🎮 NVIDIA GPU detected, installing CUDA version of PyTorch...": "🎮 NVIDIA GPU detected, installing CUDA version of PyTorch...", "🍎 MacOS detected, installing CPU version of PyTorch... Note: it might be slow during whisperX transcription.": "🍎 MacOS detected, installing CPU version of PyTorch... Note: it might be slow during whisperX transcription.", "💻 No NVIDIA GPU detected, installing CPU version of PyTorch... Note: it might be slow during whisperX transcription.": "🍎 MacOS detected, installing CPU version of PyTorch... Note: it might be slow during whisperX transcription.", "❌ Failed to install requirements:": "❌ Failed to install requirements:", "✅ FFmpeg is already installed": "✅ FFmpeg is already installed", "❌ FFmpeg not found\n\n": "❌ FFmpeg not found\n\n", "🛠️ Install using:": "🛠️ Install using:", "💡 Note:": "💡 Note:", "🔄 After installing FFmpeg, please run this installer again:": "🔄 After installing FFmpeg, please run this installer again:", "Install Chocolatey first (https://chocolatey.org/)": "Install Chocolatey first (https://chocolatey.org/)", "Install Homebrew first (https://brew.sh/)": "Install Homebrew first (https://brew.sh/)", "Use your distribution's package manager": "Use your distribution's package manager", "FFmpeg is required. Please install it and run the installer again.": "FFmpeg is required. Please install it and run the installer again.", "Installing requirements using `pip install -r requirements.txt`": "Installing requirements using `pip install -r requirements.txt`", "Installation completed": "Installation completed", "Now I will run this command to start the application:": "Now I will run this command to start the application:", "Note: First startup may take up to 1 minute": "Note: First startup may take up to 1 minute", "If the application fails to start:": "If the application fails to start:", "Check your network connection": "Check your network connection", "Re-run the installer: [bold]python install.py[/bold]": "Re-run the installer: [bold]python install.py[/bold]", "Detected NVIDIA GPU(s)": "Detected NVIDIA GPU(s)", "No NVIDIA GPU detected": "No NVIDIA GPU detected", "No NVIDIA GPU detected or NVIDIA drivers not properly installed": "No NVIDIA GPU detected or NVIDIA drivers not properly installed", "LLM JSON Format Support": "LLM JSON Format Support", "Enable if your LLM supports JSON mode output": "Enable if your LLM supports JSON mode output"}
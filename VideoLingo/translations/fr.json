{"a. Download or Upload Video": "<PERSON><PERSON> ou importer une vidéo", "Delete and Reselect": "Supprimer et resélectionner", "Enter YouTube link:": "Entrez le lien YouTube :", "Resolution": "Résolution", "Download Video": "Télécharger la vidéo", "Or upload video": "Ou importer une vidéo", "Youtube Settings": "Paramètres Youtube", "Cookies Path": "<PERSON>em<PERSON> du fi<PERSON>er <PERSON>ies", "LLM Configuration": "Configuration LLM", "API_KEY": "Clé API", "BASE_URL": "URL de base", "MODEL": "<PERSON><PERSON><PERSON><PERSON>", "Openai format, will add /v1/chat/completions automatically": "Format OpenAI, /v1/chat/completions sera ajouté automatiquement", "click to check API validity": "Cliquez pour vérifier la validité de l'API", "API Key is valid": "La clé API est valide", "API Key is invalid": "La clé API n'est pas valide", "Recog Lang": "Langue de reconnaissance", "Subtitles Settings": "Paramètres des sous-titres", "Target Lang": "Langue cible", "Input any language in natural language, as long as llm can understand": "Saisissez n'importe quelle langue en langage naturel, tant que le LLM peut la comprendre", "Vocal separation enhance": "Amélioration de la séparation vocale", "Burn-in Subtitles": "Incruster les sous-titres", "Whether to burn subtitles into the video, will increase processing time": "Pour incruster les sous-titres dans la vidéo, cela augmentera le temps de traitement", "Video Resolution": "Résolution vidéo", "Recommended for videos with loud background noise, but will increase processing time": "Recommandé pour les vidéos avec beaucoup de bruit de fond, mais augmente le temps de traitement", "Dubbing Settings": "Paramètres de doublage", "TTS Method": "Méthode TTS", "SiliconFlow API Key": "Clé API SiliconFlow", "Mode Selection": "Sélection du mode", "Preset": "Préréglage", "Refer_stable": "Référence stable", "Refer_dynamic": "Référence dynamique", "OpenAI Voice": "Voix OpenAI", "Fish TTS Character": "Personnage Fish TTS", "Azure Voice": "Voix Azure", "Please refer to Github homepage for GPT_SoVITS configuration": "Veuillez consulter la page Github pour la configuration GPT_SoVITS", "SoVITS Character": "Personnage SoVITS", "Refer Mode": "Mode de référence", "Mode 1: Use provided reference audio only": "Mode 1 : Utiliser uniquement l'audio de référence fourni", "Mode 2: Use first audio from video as reference": "Mode 2 : Utiliser le premier audio de la vidéo comme référence", "Mode 3: Use each audio from video as reference": "Mode 3 : Utiliser chaque audio de la vidéo comme référence", "Configure reference audio mode for GPT-SoVITS": "Configurer le mode audio de référence pour GPT-SoVITS", "Edge TTS Voice": "Voix Edge TTS", "=====NOTE=====": "Ce qui suit est dans st.py", "=====NOTE2=====": "Ce qui suit est dans install.py", "🚀 Starting Installation": "🚀 Démarrage de l'installation", "Do you need to auto-configure PyPI mirrors? (Recommended if you have difficulty accessing pypi.org)": "Voulez-vous configurer automatiquement les miroirs PyPI ? (Recommandé si vous avez des difficultés à accéder à pypi.org)", "🎮 NVIDIA GPU detected, installing CUDA version of PyTorch...": "🎮 GPU NVIDIA détecté, installation de la version CUDA de PyTorch...", "🍎 MacOS detected, installing CPU version of PyTorch... Note: it might be slow during whisperX transcription.": "🍎 MacOS détecté, installation de la version CPU de PyTorch... Note : la transcription whisperX peut être lente.", "💻 No NVIDIA GPU detected, installing CPU version of PyTorch... Note: it might be slow during whisperX transcription.": "💻 Aucun GPU NVIDIA détecté, installation de la version CPU de PyTorch... Note : la transcription whisperX peut être lente.", "❌ Failed to install requirements:": "❌ Échec de l'installation des prérequis :", "✅ FFmpeg is already installed": "✅ FFmpeg est déjà installé", "❌ FFmpeg not found\n\n": "❌ FFmpeg non trouvé\n\n", "🛠️ Install using:": "🛠️ Installer avec :", "💡 Note:": "💡 Note :", "🔄 After installing FFmpeg, please run this installer again:": "🔄 Après l'installation de FFmpeg, veuillez relancer cet installateur :", "Install Chocolatey first (https://chocolatey.org/)": "Installez d'abord Chocolatey (https://chocolatey.org/)", "Install Homebrew first (https://brew.sh/)": "Installez d'abord Homebrew (https://brew.sh/)", "Use your distribution's package manager": "Utilisez le gestionnaire de paquets de votre distribution", "FFmpeg is required. Please install it and run the installer again.": "FFmpeg est requis. Veuillez l'installer et relancer l'installateur.", "Installation completed": "Installation terminée", "Now I will run this command to start the application:": "Je vais maintenant exécuter cette commande pour démarrer l'application :", "Note: First startup may take up to 1 minute": "Note : Le premier démarrage peut prendre jusqu'à 1 minute", "If the application fails to start:": "Si l'application ne démarre pas :", "Check your network connection": "Vérifiez votre connexion réseau", "Re-run the installer: [bold]python install.py[/bold]": "Relancez l'installateur : [bold]python install.py[/bold]", "Installing requirements using `pip install -r requirements.txt`": "Installation des dépendances avec `pip install -r requirements.txt`", "b. Translate and Generate Subtitles": "b. <PERSON><PERSON><PERSON><PERSON> et gén<PERSON>rer les sous-titres", "This stage includes the following steps:": "Cette étape comprend les étapes suivantes :", "WhisperX word-level transcription": "Transcription au niveau des mots WhisperX", "Sentence segmentation using NLP and LLM": "Segmentation des phrases utilisant NLP et LLM", "Summarization and multi-step translation": "Résumé et traduction en plusieurs étapes", "Cutting and aligning long subtitles": "Découpage et alignement des longs sous-titres", "Generating timeline and subtitles": "Génération de la chronologie et des sous-titres", "Merging subtitles into the video": "Fusion des sous-titres dans la vidéo", "Start Processing Subtitles": "Démarrer le traitement des sous-titres", "Download All Srt Files": "Télécharger tous les fichiers Srt", "Archive to 'history'": "Archiver dans 'history'", "Using Whisper for transcription...": "Utilisation de Whisper pour la transcription...", "Splitting long sentences...": "Division des longues phrases...", "Summarizing and translating...": "Résumé et traduction en cours...", "Processing and aligning subtitles...": "Traitement et alignement des sous-titres...", "Merging subtitles to video...": "Fusion des sous-titres dans la vidéo...", "⚠️ PAUSE_BEFORE_TRANSLATE. Go to `output/log/terminology.json` to edit terminology. Then press ENTER to continue...": "⚠️ PAUSE_AVANT_TRADUCTION. Allez dans `output/log/terminology.json` pour éditer la terminologie. Puis appuyez sur ENTRÉE pour continuer...", "Subtitle processing complete! 🎉": "Traitement des sous-titres terminé ! 🎉", "c. Dubbing": "<PERSON><PERSON>", "Generate audio tasks and chunks": "Générer les tâches audio et les segments", "Extract reference audio": "Extraire l'audio de référence", "Generate and merge audio files": "Générer et fusionner les fichiers audio", "Merge final audio into video": "Fusionner l'audio final dans la vidéo", "Start Audio Processing": "Démarrer le traitement audio", "Audio processing is complete! You can check the audio files in the `output` folder.": "Le traitement audio est terminé ! Vous pouvez vérifier les fichiers audio dans le dossier `output`.", "Delete dubbing files": "Supp<PERSON>er les fichiers de doublage", "Generate audio tasks": "Générer les tâches audio", "Extract refer audio": "Extraire l'audio de référence", "Generate all audio": "<PERSON><PERSON><PERSON><PERSON> tout l'audio", "Merge full audio": "Fusionner l'audio complet", "Merge dubbing to the video": "Fusionner le doublage dans la vidéo", "Audio processing complete! 🎇": "Traitement audio terminé ! 🎇", "Hello, welcome to VideoLingo. If you encounter any issues, feel free to get instant answers with our Free QA Agent <a href=\"https://share.fastgpt.in/chat/share?shareId=066w11n3r9aq6879r4z0v9rh\" target=\"_blank\">here</a>! You can also try out our SaaS website at <a href=\"https://videolingo.io\" target=\"_blank\">videolingo.io</a> for free!": "Bon<PERSON>r, bienvenue sur VideoLingo. Si vous rencontrez des problèmes, n'hésitez pas à obtenir des réponses instantanées avec notre Agent QA gratuit <a href=\"https://share.fastgpt.in/chat/share?shareId=066w11n3r9aq6879r4z0v9rh\" target=\"_blank\">ici</a> ! Vous pouvez également essayer gratuitement notre site web SaaS sur <a href=\"https://videolingo.io\" target=\"_blank\">videolingo.io</a> !", "WhisperX Runtime": "Environnement WhisperX", "Local runtime requires >8GB GPU, cloud runtime requires 302ai API key, elevenlabs runtime requires ElevenLabs API key": "L'environnement local nécessite un GPU >8GB, l'environnement cloud nécessite une clé API 302ai, l'environnement elevenlabs nécessite une clé API ElevenLabs", "WhisperX 302ai API": "API 302ai WhisperX", "Detected NVIDIA GPU(s)": "GPU(s) NVIDIA détecté(s)", "No NVIDIA GPU detected": "Aucun GPU NVIDIA détecté", "No NVIDIA GPU detected or NVIDIA drivers not properly installed": "Aucun GPU NVIDIA détecté ou pilotes NVIDIA mal installés", "LLM JSON Format Support": "Support du format JSON pour LLM", "Enable if your LLM supports JSON mode output": "Activer si votre LLM prend en charge la sortie en mode JSON"}
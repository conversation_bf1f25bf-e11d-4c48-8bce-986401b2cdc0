import os
import warnings
import time
import subprocess
import torch
import mlx_whisper
import lightning_whisper_mlx
import librosa
import numpy as np
from rich import print as rprint
from core.utils import *

warnings.filterwarnings("ignore")
MODEL_DIR = load_key("model_dir")

@except_handler("failed to check hf mirror", default_return=None)
def check_hf_mirror():
    """Check HuggingFace mirror speed for MLX models"""
    mirrors = {'Official': 'huggingface.co', 'Mirror': 'hf-mirror.com'}
    fastest_url = f"https://{mirrors['Official']}"
    best_time = float('inf')
    rprint("[cyan]🔍 Checking HuggingFace mirrors for MLX models...[/cyan]")
    for name, domain in mirrors.items():
        if os.name == 'nt':
            cmd = ['ping', '-n', '1', '-w', '3000', domain]
        else:
            cmd = ['ping', '-c', '1', '-W', '3', domain]
        start = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True)
        response_time = time.time() - start
        if result.returncode == 0:
            if response_time < best_time:
                best_time = response_time
                fastest_url = f"https://{domain}"
            rprint(f"[green]✓ {name}:[/green] {response_time:.2f}s")
    if best_time == float('inf'):
        rprint("[yellow]⚠️ All mirrors failed, using default[/yellow]")
    rprint(f"[cyan]🚀 Selected mirror:[/cyan] {fastest_url} ({best_time:.2f}s)")
    return fastest_url

@except_handler("MLX-Whisper processing error:")
def transcribe_audio(raw_audio_file, vocal_audio_file, start, end):
    """
    🔥 MLX-Whisper integration for VideoLingo
    Optimized for Apple Silicon with 10x speedup via Lightning-Whisper-MLX
    Мастер Ика + Claude - Команда Воинов!
    """
    os.environ['HF_ENDPOINT'] = check_hf_mirror()
    WHISPER_LANGUAGE = load_key("whisper.language")
    
    # MLX works best on Apple Silicon
    device = "mps" if torch.backends.mps.is_available() else "cpu"
    rprint(f"🔥 Starting MLX-Whisper using device: {device} (Apple Silicon optimized) ...")
    
    # Optimized batch size for Apple Silicon
    batch_size = load_key("whisper.batch_size", default=12)
    rprint(f"[cyan]⚡ MLX Batch size:[/cyan] {batch_size} (Lightning optimized)")
    rprint(f"[green]▶️ Starting MLX-Whisper for segment {start:.2f}s to {end:.2f}s...[/green]")
    
    # Model selection for MLX
    if WHISPER_LANGUAGE == 'zh':
        # For Chinese, use specialized model if available
        model_name = "mlx-community/whisper-large-v3-mlx"
        rprint(f"[yellow]🇨🇳 Chinese detected - using large-v3 model[/yellow]")
    else:
        model_name = load_key("whisper.model", default="mlx-community/whisper-large-v3-mlx")
        rprint(f"[green]🌍 Multi-language model:[/green] {model_name}")
    
    def load_audio_segment(audio_file, start, end):
        """Load audio segment optimized for MLX"""
        audio, sr = librosa.load(audio_file, sr=16000, offset=start, duration=end - start, mono=True)
        return audio
    
    raw_audio_segment = load_audio_segment(raw_audio_file, start, end)
    vocal_audio_segment = load_audio_segment(vocal_audio_file, start, end)
    
    # -------------------------
    # 1. MLX-Whisper transcription with Lightning speedup
    # -------------------------
    transcribe_start_time = time.time()
    rprint("[bold green]🔥 Lightning-Whisper-MLX processing (10x speedup) ↓[/bold green]")
    
    try:
        # Try Lightning-Whisper-MLX first for maximum speed
        lightning_whisper = lightning_whisper_mlx.LightningWhisperMLX(
            model=model_name,
            batch_size=batch_size,
            quant=None  # Use full precision for best quality
        )
        
        # Transcribe with Lightning-Whisper-MLX
        whisper_language = None if 'auto' in WHISPER_LANGUAGE else WHISPER_LANGUAGE
        result = lightning_whisper.transcribe(
            raw_audio_segment,
            language=whisper_language
        )
        
        rprint("[green]⚡ Lightning-Whisper-MLX completed successfully![/green]")
        
    except Exception as e:
        rprint(f"[yellow]⚠️ Lightning-Whisper-MLX failed: {e}[/yellow]")
        rprint("[cyan]🔄 Falling back to standard MLX-Whisper...[/cyan]")
        
        # Fallback to standard MLX-Whisper
        result = mlx_whisper.transcribe(
            raw_audio_segment,
            path_or_hf_repo=model_name,
            verbose=True
        )
    
    transcribe_time = time.time() - transcribe_start_time
    rprint(f"[cyan]⏱️ MLX transcribe time:[/cyan] {transcribe_time:.2f}s")
    
    # Save detected language
    detected_language = result.get('language', WHISPER_LANGUAGE)
    update_key("whisper.language", detected_language)
    
    if detected_language == 'zh' and WHISPER_LANGUAGE != 'zh':
        raise ValueError("Please specify the transcription language as zh and try again!")
    
    # -------------------------
    # 2. Process segments for VideoLingo compatibility
    # -------------------------
    align_start_time = time.time()
    rprint("[cyan]🔧 Processing segments for VideoLingo...[/cyan]")
    
    # Convert MLX result to VideoLingo format
    segments = []
    if 'segments' in result:
        for segment in result['segments']:
            # Adjust timestamps to absolute time
            segment_data = {
                'start': segment.get('start', 0) + start,
                'end': segment.get('end', 0) + start,
                'text': segment.get('text', '').strip(),
                'words': []
            }
            
            # Process word-level timestamps if available
            if 'words' in segment:
                for word in segment['words']:
                    word_data = {
                        'word': word.get('word', ''),
                        'start': word.get('start', 0) + start,
                        'end': word.get('end', 0) + start,
                        'score': word.get('probability', 1.0)
                    }
                    segment_data['words'].append(word_data)
            
            segments.append(segment_data)
    
    align_time = time.time() - align_start_time
    rprint(f"[cyan]⏱️ MLX segment processing:[/cyan] {align_time:.2f}s")
    
    # Return in VideoLingo expected format
    result_formatted = {
        'segments': segments,
        'language': detected_language
    }
    
    rprint(f"[green]🎉 MLX-Whisper completed! Total segments: {len(segments)}[/green]")
    return result_formatted

def get_model_info():
    """Get MLX model information for diagnostics"""
    try:
        import mlx.core as mx
        return {
            'framework': 'MLX-Whisper + Lightning',
            'device': 'Apple Silicon (MPS)',
            'optimization': '10x speedup',
            'status': '🔥 Ready'
        }
    except Exception as e:
        return {
            'framework': 'MLX-Whisper',
            'status': f'❌ Error: {e}'
        }

{"mcpServers": {"memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {}}, "mcp-shrimp-task-manager": {"command": "npx", "args": ["/Users/<USER>/Projects/handmade/node-native/mcp-shrimp-task-manager/dist/index.js"], "env": {"DATA_DIR": "/Volumes/SAB500/VideoLingo", "TEMPLATES_USE": "en", "ENABLE_GUI": "false"}}, "n8n-mcp-server": {"command": "npx", "args": ["n8n-mcp-server"], "env": {"N8N_API_URL": "http://localhost:5678/api/v1", "N8N_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlNWNkYmI0MC1jZTc1LTQzNWMtOTgwZS1mNTA4YTMxZmMwNDIiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUwNTU4Nzc0fQ.1s29_KYwpF918F_fd01by9JVftgeFF-7DKUJdgPCAzU"}}, "git-server": {"command": "/usr/local/bin/uv", "args": ["--directory", "/Users/<USER>/Projects/MCP_SERVS/servers/src/git", "run", "python", "-m", "mcp_server_git"], "env": {}}, "fetch-server": {"command": "/usr/local/bin/uv", "args": ["--directory", "/Users/<USER>/Projects/MCP_SERVS/servers/src/fetch", "run", "python", "-m", "mcp_server_fetch"], "env": {}}, "godly-filesystem": {"command": "node", "args": ["/Users/<USER>/Projects/handmade/node-rewritten/filesystem-manager/index.js"], "env": {}}, "godly-ssl": {"command": "node", "args": ["/Users/<USER>/Projects/handmade/node-rewritten/ssl-manager/index.js"], "env": {}}, "godly-timer": {"command": "node", "args": ["/Users/<USER>/Projects/handmade/node-rewritten/timer-manager/index.js"], "env": {"TELEGRAM_BOT_TOKEN": "**********************************************"}}, "timer-mcp-server": {"command": "node", "args": ["/Users/<USER>/Projects/MCP_SERVS/timer-mcp-server/dist/index.js"], "env": {}}}}
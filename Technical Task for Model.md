Technical Task for Model:

Develop an automated video creation system similar to the one demonstrated in the following video: https://www.youtube.com/watch?v=ivty6t0lUkQ&t=312s.

Create a comparable, yet significantly enhanced application specifically designed for our YouTube channel, @GodlySharing.

The user interface (UI) and user experience (UX) should be modern and user-friendly, ensuring seamless interaction with the application. However, it is imperative to note that this application will be exclusively utilized for our project. The repository on GitHub will remain private indefinitely.

The backend should prioritize speed and efficiency, employing a comprehensive set of functions to consistently generate content and foster the growth of our community. The process illustrated is a potent amalgamation of various AI tools and a no-code automation platform.

High-Level Framework: “Godly Sharing” Agent

Conceive this system as two distinct AI “agencies” collaborating seamlessly:

* Ideas Agency (Content Planner): This component, powered by a technology such as Gemini 2.5 Flash, will extract a central theme (e.g., “verses about hope”) and generate a comprehensive list of Bible verses, along with prompts for visual elements and narration styles. This serves as your content calendar.

Creator Agency (Your Production House): This represents the automated workflow you will construct. It will retrieve one entry from your content calendar daily, generate the video incorporating visuals, a soothing voiceover, and background music, and subsequently publish it to our YouTube channel.

Essential Tools (Toolkit):

* n8n.io (The Automation Engine): This no-code platform, as demonstrated in the video, enables the creation of workflows that connect various tools. It is already integrated with Google services via API. We have received a complimentary free tier to initiate our project.

* Google Account (For Sheets): This serves as our database and content calendar. It is free of charge. However, we have subscribed to Google Workspace, which includes our domain and email address.

* Gemini Flash API Key (For Ideas & Scripts): This key facilitates the generation of video ideas and narration scripts. It is a cost-effective solution for this purpose.

* JSON2Video Account (The Video Engine): We do not require this account as we have a pre-built solution based on FFmpeg (files with code attached).

ElevenLabs Account (For Voiceovers): This account enables the creation of high-quality, realistic voiceovers. ElevenLabs offers a free tier suitable for testing purposes. We will utilize Google Services for this purpose.

**Suno AI (Optional for Music Generation):** This service enables the generation of custom, royalty-free background music that aligns with the project’s theme. Google Services will be utilized for this functionality.

**YouTube API Credentials:** These credentials facilitate n8n’s direct upload of videos to our YouTube channel. Setting them up is cost-free and can be accomplished through your Google Cloud account. (Credentials have already been generated.)

By centralizing our environment (models, services, etc.) on a singular, robust platform, we can optimize our operations and achieve substantial improvements.

**Phase 1: Input - Your Content Pipeline**
**Phase 1: Content Preparation and Collection**
**Step 1: Establishing Google Sheet <NAME_EMAIL>**
* Create a new Google Sheet.
* In the first tab (renamed ContentQueue), create the following columns:
    * idVerse_ReferenceVerse_TextTheme_PromptVisual_Style_PromptProduction_StatusFinal_Video_URLPublishing_Status
* **Example:**
    * John 3:16
        * For God so loved…
        * Hope, Salvation, Love
        * Serene, Heavenly light, soft focus, sunrise
        * For production pending
* **Step 2: Crafting “Ideas Agent” Prompt for Gemini 2.5 Flash**
* API-connected Gemini utilizes prompts similar to this, specifically tailored for your @GodlySharing channel.
* **Example Gemini Prompt:**
    * “Act as a content planner for the YouTube channel ‘@GodlySharing’. The channel creates short, inspirational videos based on Bible verses.”
* **Your Task:**
**Step 3: Populate Your Sheet**
Copy the table generated by Gemini Flash 2.5 and paste it into your ContentQueue Google Sheet, commencing from the second row (under the headers).

You now have a queue of content prepared for your automation.

**Phase 2: The Creation & Publishing - Constructing the n8n Workflow - Selecting Clara AI Assistant**

This phase entails constructing the workflow depicted in the video. The logic remains the same; we are merely providing it with distinct content.

**Note:** JSON2Video ElevenLabs Account Suno AI, we change to same product made by google even if product is beta





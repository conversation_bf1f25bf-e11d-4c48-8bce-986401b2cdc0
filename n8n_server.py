import os
import socket
import subprocess

def start_n8n_mcp_server():
    try:
        # Check if n8n is installed and runnable
        subprocess.run(["n8n", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("n8n is not found or not runnable. Please ensure n8n is installed.")
        return

    try:
        # Attempt to start n8n, typically it runs on port 5678
        print("Attempting to start n8n MCP Server...")
        process = subprocess.Popen(["n8n"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        # Give it a moment to start and check if the port is listening
        import time
        time.sleep(5)  # Wait for 5 seconds for n8n to try and start

        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', 5678))
        if result == 0:
            print("n8n MCP Server is likely running and listening on port 5678.")
        else:
            print(f"n8n MCP Server is not listening on port 5678. Error code: {result}")
            stdout, stderr = process.communicate(timeout=1)
            print("n8n stdout:", stdout)
            print("n8n stderr:", stderr)
            print("\nPossible network issues: Port 5678 might be blocked by a firewall, or another process is already using it.")
            print("You can check listening ports with 'netstat -tuln' or 'ss -tuln'.")
            print("You may also need to check firewall rules (e.g., 'sudo ufw status' for Ubuntu).")

    except Exception as e:
        print(f"An error occurred while trying to start n8n MCP Server: {e}")

if __name__ == "__main__":
    start_n8n_mcp_server()

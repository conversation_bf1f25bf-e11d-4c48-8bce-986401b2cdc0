{"mcpServers": {"mcp-installer": {"command": "npx", "args": ["@anaisbetts/mcp-installer"], "env": {}}, "context7-mcp": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@upstash/context7-mcp", "--key", "7122bec7-4656-4efe-a4a2-12921a055ec6"], "env": {}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}}, "desktop-commander": {"command": "npx", "args": ["@wonderwhy-er/desktop-commander@latest"], "env": {}}, "servers": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@jlia0/servers", "--key", "b523f26f-db45-4d27-bbda-225aa68a616e", "--profile", "salty-wildfowl-03J83F"], "env": {}}, "toolbox": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@smithery/toolbox", "--key", "7122bec7-4656-4efe-a4a2-12921a055ec6", "--profile", "renewed-whale-FrWczB"], "env": {}}, "mcp-shrimp-task-manager": {"command": "npx", "args": ["/Users/<USER>/Projects/handmade/node-native/mcp-shrimp-task-manager/dist/index.js"], "env": {"DATA_DIR": "/Volumes/SAB500/VideoLingo", "TEMPLATES_USE": "en", "ENABLE_GUI": "false"}}, "ika-compressor-mcp": {"command": "npx", "args": ["-y", "/Users/<USER>/Projects/handmade/python-original/ika-compressor-mcp"], "env": {"PYTHON_PATH": "python3"}}, "timer-mcp-server": {"command": "node", "args": ["/Users/<USER>/Projects/MCP_SERVS/timer-mcp-server/dist/index.js"], "env": {}}, "godly-filesystem": {"command": "node", "args": ["/Users/<USER>/Projects/handmade/node-rewritten/filesystem-manager/index.js"], "env": {}}, "godly-ssl": {"command": "node", "args": ["/Users/<USER>/Projects/handmade/node-rewritten/ssl-manager/index.js"], "env": {}}}}
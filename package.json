{"name": "mcp-superassistant", "version": "0.3.1", "description": "MCP SuperAssistant", "license": "MIT", "private": true, "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/srbhptl39/MCP-SuperAssistant.git"}, "type": "module", "scripts": {"clean:bundle": "rimraf dist && turbo clean:bundle", "clean:node_modules": "pnpm dlx rimraf node_modules && pnpm dlx turbo clean:node_modules", "clean:turbo": "rimraf .turbo && turbo clean:turbo", "clean": "pnpm clean:bundle && pnpm clean:turbo && pnpm clean:node_modules", "clean:install": "pnpm clean:node_modules && pnpm install --frozen-lockfile", "type-check": "turbo type-check", "base-build": "pnpm clean:bundle && turbo build", "build": "pnpm set-global-env && pnpm base-build", "build:firefox": "pnpm set-global-env CLI_CEB_FIREFOX=true && pnpm base-build", "base-dev": "pnpm clean:bundle && turbo ready && turbo watch dev --concurrency 20", "dev": "pnpm set-global-env CLI_CEB_DEV=true && pnpm base-dev", "dev:firefox": "pnpm set-global-env CLI_CEB_DEV=true CLI_CEB_FIREFOX=true && pnpm base-dev", "build:eslint": "tsc -b", "zip": "pnpm build && pnpm -F zipper zip", "zip:firefox": "pnpm build:firefox && pnpm -F zipper zip", "e2e": "pnpm zip && turbo e2e", "e2e:firefox": "pnpm zip:firefox && turbo e2e", "lint": "turbo lint --continue -- --fix --cache --cache-location node_modules/.cache/.eslintcache", "lint:fix": "turbo lint:fix --continue -- --fix --cache --cache-location node_modules/.cache/.eslintcache", "prettier": "turbo prettier --continue -- --cache --cache-location node_modules/.cache/.prettiercache", "prepare": "husky", "update-version": "bash bash-scripts/update_version.sh", "copy_env": "bash bash-scripts/copy_env.sh", "set-global-env": "bash bash-scripts/set_global_env.sh", "postinstall": "pnpm build:eslint && pnpm copy_env", "module-manager": "pnpm -F module-manager start"}, "dependencies": {"react": "19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/compat": "^1.3.0", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.29.0", "@types/chrome": "^0.0.326", "@types/eslint-plugin-jsx-a11y": "^6.10.0", "@types/eslint__eslintrc": "^2.1.2", "@types/node": "^22.5.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "deepmerge": "^4.3.1", "esbuild": "^0.25.5", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-import": "^2.32.0", "eslint-plugin-import-x": "^4.15.2", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-tailwindcss": "^3.17.5", "fast-glob": "^3.3.3", "globals": "^15.15.0", "husky": "^9.1.4", "lint-staged": "^15.5.2", "postcss": "^8.5.6", "postcss-load-config": "^6.0.1", "prettier": "^3.3.3", "rimraf": "^6.0.1", "run-script-os": "^1.1.6", "tailwindcss": "^3.4.17", "tslib": "^2.8.1", "turbo": "^2.5.4", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1", "vite": "^6.3.5"}, "lint-staged": {"*.{js,jsx,ts,tsx,json}": ["prettier --write"]}, "packageManager": "pnpm@9.15.1", "engines": {"node": ">=22.12.0"}}
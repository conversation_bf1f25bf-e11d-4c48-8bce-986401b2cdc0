{"mcpServers": {"mcp-installer": {"command": "npx", "args": ["@anaisbetts/mcp-installer"], "env": {}}, "whois-mcp": {"command": "npx", "args": ["/Users/<USER>/whois-mcp/dist/index.js"], "env": {}}, "context7-mcp": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@upstash/context7-mcp", "--key", "${CONTEXT7_MCP_KEY}"], "env": {}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}}, "whatsapp": {"command": "/usr/local/bin/uv", "args": ["--directory", "/Volumes/DATA/whatsapp-mcp/whatsapp-mcp-server", "run", "main.py"], "env": {}}, "desktop-commander": {"command": "npx", "args": ["@wonderwhy-er/desktop-commander@latest"], "env": {}}, "servers": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@jlia0/servers", "--key", "${SMITHERY_SERVERS_KEY}", "--profile", "${SMITHERY_SERVERS_PROFILE}"], "env": {}}, "server-cmd": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "server-cmd", "--key", "${SMITHERY_SERVERS_KEY}", "--profile", "${SMITHERY_SERVERS_PROFILE}"], "env": {}}, "toolbox": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@smithery/toolbox", "--key", "${CONTEXT7_MCP_KEY}", "--profile", "${SMITHERY_TOOLBOX_PROFILE}"], "env": {}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Downloads", "/Volumes/DATA", "/Users/<USER>/Projects/MCP_SERVS", "/Volumes/SAB500/our_mcp_server_client", "/Volumes/DATA2/cua"], "env": {}}, "mcp-shrimp-task-manager": {"command": "npx", "args": ["/Volumes/SAB500/mcp-shrimp-task-manager/dist/index.js"], "env": {"DATA_DIR": "/Volumes/SAB500/VideoLingo", "TEMPLATES_USE": "en", "ENABLE_GUI": "false"}}, "ika-compressor-mcp": {"command": "npx", "args": ["-y", "/Users/<USER>/Projects/MCP_SERVS/ika-compressor-mcp"], "env": {"PYTHON_PATH": "python"}}, "youtube-analytics-stable": {"command": "node", "args": ["/Users/<USER>/Downloads/godly-sharing-automation/mcp-servers/youtube-analytics/index.js"], "env": {}}, "videolingo-task-manager": {"command": "node", "args": ["/Users/<USER>/Downloads/godly-sharing-automation/mcp-servers/task-manager/index.js"], "env": {}}, "mcp-devcontainers": {"command": "npx", "args": ["@crunchloop/mcp-devcontainers"], "env": {}}, "time": {"command": "C:\\Users\\<USER>\\.local\\bin\\uv.exe", "args": ["--directory", "F:\\mcp-servers\\src\\time", "run", "python", "-m", "mcp_server_time", "--local-timezone", "Asia/Jerusalem"], "env": {}}, "memory-windows": {"command": "npx", "args": ["F:\\mcp-servers\\src\\memory\\dist\\index.js"], "env": {}}, "mcp-server-docker": {"command": "uvx", "args": ["mcp-server-docker"], "env": {}}}}
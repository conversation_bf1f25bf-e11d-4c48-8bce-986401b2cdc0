# 🚀 Godly Sharing MCP Servers

Собственные MCP серверы для проекта Godly Sharing, разработанные для обеспечения стабильности, безопасности и специализации под наши задачи.

## 📁 Структура проекта

```
godly-sharing-mcp/
├── filesystem-manager/     # Безопасная работа с файловой системой
├── youtube-advanced/       # Продвинутая работа с YouTube API
├── youtube-analytics/      # Аналитика канала Godly Sharing  
└── videolingo-planner/     # Планировщик VideoLingo проекта
```

## 🔧 Серверы

### 1. **Filesystem Manager** (`godly-filesystem`)
**Назначение:** Безопасная работа с файловой системой  
**Статус:** ✅ Готов к использованию

**Функции:**
- Чтение/запись файлов в разрешенных директориях
- Создание директорий и получение статистики
- Поиск файлов по паттернам
- Рекурсивное сканирование

**Безопасность:** Доступ ограничен предустановленными путями

### 2. **YouTube Advanced** (`godly-youtube`)  
**Назначение:** Расширенная работа с YouTube API  
**Статус:** ⚠️ Требует API ключ

**Функции:**
- Поиск видео с продвинутыми фильтрами
- Получение транскриптов и субтитров
- Анализ вовлеченности и метрик
- Специальная аналитика для Godly Sharing
- Работа с трендами и каналами

**Требования:** YouTube Data API v3 ключ

### 3. **YouTube Analytics** (`godly-analytics`)
**Назначение:** Специализированная аналитика  
**Статус:** ✅ Готов к использованию

**Функции:**  
- Статистика канала Godly Sharing
- Анализ роста аудитории
- Метрики вовлеченности
- Рекомендации по развитию

## 🔑 Настройка

### 1. Установка зависимостей
```bash
# Filesystem Manager
cd filesystem-manager && npm install

# YouTube Advanced  
cd youtube-advanced && npm install

# YouTube Analytics (уже готов)
cd youtube-analytics && npm install
```

### 2. Конфигурация Claude Desktop
Обновите файл `~/.claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "godly-filesystem": {
      "command": "node",
      "args": ["/path/to/filesystem-manager/index.js"],
      "env": {}
    },
    "godly-youtube": {
      "command": "node", 
      "args": ["/path/to/youtube-advanced/index.js"],
      "env": {
        "YOUTUBE_API_KEY": "your_youtube_api_key_here"
      }
    },
    "godly-analytics": {
      "command": "node",
      "args": ["/path/to/youtube-analytics/index.js"],
      "env": {}
    }
  }
}
```

### 3. YouTube API Setup
1. Перейдите в [Google Cloud Console](https://console.cloud.google.com/)
2. Создайте проект или выберите существующий  
3. Включите YouTube Data API v3
4. Создайте API ключ (без ограничений для тестирования)
5. Добавьте ключ в конфигурацию Claude

## 🧪 Тестирование

### Filesystem Manager
```bash
# Запуск сервера
cd filesystem-manager
node index.js

# В новом терминале - тестовые команды через MCP
```

### YouTube Advanced
```bash
# Запуск с API ключом
cd youtube-advanced
YOUTUBE_API_KEY="your_key" node index.js
```

### YouTube Analytics  
```bash
# Запуск сервера
cd youtube-analytics
node index.js
```

## 🔒 Безопасность

### Filesystem Manager
- **Whitelist директорий:** Доступ только к предустановленным путям
- **Валидация путей:** Проверка на попытки обхода ограничений
- **Безопасное чтение:** Защита от чтения системных файлов

### YouTube Advanced
- **API Rate Limiting:** Соблюдение лимитов YouTube API
- **Error Handling:** Безопасная обработка ошибок API
- **Input Validation:** Валидация всех входных параметров

## 📊 Мониторинг

### Логи
Все серверы выводят логи в stderr:
- Статус подключения
- Ошибки выполнения
- Информация о запросах

### Отладка
Для отладки используйте:
```bash
DEBUG=mcp* node index.js
```

## 🔄 Обновления

### Версионирование
Каждый сервер имеет версию в `package.json`:
- `filesystem-manager`: v1.0.0
- `youtube-advanced`: v1.0.0  
- `youtube-analytics`: v1.0.0

### Добавление функций
1. Обновите код сервера
2. Добавьте новые инструменты в `setupToolHandlers()`
3. Обновите версию в `package.json`
4. Перезапустите Claude Desktop

## 🚀 Интеграция с VideoLingo

Серверы разработаны с учетом интеграции с VideoLingo проектом:

- **Filesystem:** Доступ к проектным файлам VideoLingo
- **YouTube:** Анализ видео для обработки
- **Analytics:** Метрики для принятия решений

## 📝 Разработка

### Добавление нового сервера
1. Создайте директорию в `godly-sharing-mcp/`
2. Инициализируйте `package.json`
3. Создайте `index.js` с MCP Server структурой
4. Добавьте в конфигурацию Claude Desktop

### Шаблон сервера
```javascript
#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';

class YourMCPServer {
  constructor() {
    this.server = new Server({
      name: 'your-mcp-server',
      version: '1.0.0',
    }, {
      capabilities: { tools: {} },
    });
    
    this.setupToolHandlers();
  }
  
  // Реализация инструментов...
}

const server = new YourMCPServer();
server.run().catch(console.error);
```

## 🆘 Поддержка

При возникновении проблем:

1. **Проверьте логи** - stderr вывод серверов
2. **Валидируйте конфигурацию** - корректность JSON
3. **Проверьте API ключи** - валидность YouTube API
4. **Перезапустите Claude Desktop** - после изменений

## 📈 Roadmap

- [ ] Добавление Telegram MCP сервера
- [ ] Интеграция с WhatsApp для уведомлений  
- [ ] Расширение аналитики для других платформ
- [ ] Автоматическое тестирование серверов
- [ ] CI/CD pipeline для обновлений

---

**🎯 Создано командой Ika для проекта Godly Sharing**

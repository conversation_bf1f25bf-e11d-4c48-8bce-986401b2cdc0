{"mcpServers": {"memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {}}, "mcp-shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager"], "env": {"DATA_DIR": "/Users/<USER>/Projects/MCP_TOOLS/data", "TEMPLATES_USE": "en", "ENABLE_GUI": "false"}}, "n8n-mcp-server": {"command": "npx", "args": ["n8n-mcp-server"], "env": {"N8N_API_URL": "http://localhost:5678/api/v1", "N8N_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlNWNkYmI0MC1jZTc1LTQzNWMtOTgwZS1mNTA4YTMxZmMwNDIiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUwNTU4Nzc0fQ.1s29_KYwpF918F_fd01by9JVftgeFF-7DKUJdgPCAzU"}}, "youtube-advanced": {"command": "node", "args": ["/Users/<USER>/Projects/MCP_SERVS/youtube-advanced/index.js"], "env": {"YOUTUBE_API_KEY": "AIzaSyAeI-U4Et6xrWx4IX5Hol14eZyJAk0UGfU"}}, "git-server": {"command": "/usr/local/bin/uv", "args": ["--directory", "/Users/<USER>/Projects/MCP_SERVS/servers/src/git", "run", "python", "-m", "mcp_server_git"], "env": {"GITHUB_TOKEN": "*********************************************************************************************"}}}}
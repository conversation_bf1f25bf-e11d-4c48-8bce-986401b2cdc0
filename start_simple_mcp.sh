#!/bin/bash

# Простой и надежный запуск MCP сервера
# Дата: 2025-06-26

set -e

echo "🚀 Запуск простого MCP сервера..."
echo "📁 Рабочая директория: $(pwd)"
echo "⏰ Время: $(date)"

# Проверка файлов
if [ ! -f "mcpconfig_simple.json" ]; then
    echo "❌ Файл mcpconfig_simple.json не найден"
    exit 1
fi

echo "✅ Конфигурация найдена"

# Остановка старых процессов
echo "🛑 Остановка старых процессов..."
pkill -f "mcp-superassistant-proxy" 2>/dev/null || echo "Нет старых процессов"
sleep 2

# Проверка порта
echo "🔍 Проверка порта 3006..."
if lsof -i:3006 >/dev/null 2>&1; then
    echo "⚠️ Порт 3006 занят, освобождаем..."
    lsof -ti:3006 | xargs kill -9 2>/dev/null || true
    sleep 2
fi

echo "✅ Порт 3006 свободен"

# Проверка Node.js
echo "🔍 Проверка Node.js..."
node --version
npx --version

# Запуск сервера
echo "🚀 Запуск MCP SuperAssistant Proxy..."
echo "🌐 URL: http://localhost:3006"
echo "📡 Transport: SSE"
echo "⚙️ Config: mcpconfig_simple.json"
echo ""
echo "Для остановки нажмите Ctrl+C"
echo "================================"

# Запуск с логированием
exec npx @srbhptl39/mcp-superassistant-proxy@latest \
    --config ./mcpconfig_simple.json \
    --outputTransport sse \
    --host 0.0.0.0 \
    --port 3006 \
    --verbose

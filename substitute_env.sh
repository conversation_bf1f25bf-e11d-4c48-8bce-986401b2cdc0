#!/bin/bash

# Загружаем переменные окружения из .env файла
set -a
source .env
set +a

echo "Загружены переменные окружения из .env"
echo "Создаем конфигурацию с подставленными переменными..."

# Используем envsubst для подстановки переменных
envsubst < mcpconfig.json > mcpconfig.resolved.json

echo "✅ Конфигурация с подставленными переменными сохранена в mcpconfig.resolved.json"

# Проверяем валидность JSON
if node -e "JSON.parse(require('fs').readFileSync('mcpconfig.resolved.json', 'utf8')); console.log('✅ JSO<PERSON> синтаксис корректен')"; then
    echo "Запускаем MCP SuperAssistant Proxy..."
    npx @srbhptl39/mcp-superassistant-proxy@latest --config ./mcpconfig.resolved.json --outputTransport sse
else
    echo "❌ Ошибка в JSON синтаксисе"
    exit 1
fi

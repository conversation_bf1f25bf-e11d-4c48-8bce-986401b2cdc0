{
  "name": "YouTube Long Video Automation",
  "nodes": [
    {
      "parameters": {
        "rule": {
          "interval": [
            {
              "field": "cronExpression",
              "expression": "0 */2 * * *"
            }
          ]
        }
      },
      "id": "schedule-trigger",
      "name": "Каждые 2 часа",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.2,
      "position": [
        240,
        300
      ]
    },
    {
      "parameters": {
        "assignments": {
          "assignments": [
            {
              "id": "video_niche",
              "name": "video_niche",
              "value": "технологии и ИИ",
              "type": "string"
            },
            {
              "id": "target_duration",
              "name": "target_duration", 
              "value": "25-35 минут",
              "type": "string"
            },
            {
              "id": "video_style",
              "name": "video_style",
              "value": "образовательный, профессиональный",
              "type": "string"
            }
          ]
        }
      },
      "id": "set-config",
      "name": "Конфигурация видео",
      "type": "n8n-nodes-base.set",
      "typeVersion": 3.4,
      "position": [
        440,
        300
      ]
    },
    {
      "parameters": {
        "resource": "text",
        "operation": "message",
        "model": "gpt-4o",
        "messages": {
          "messages": [
            {
              "role": "system",
              "content": "Ты - креативный продюсер YouTube канала. Создаешь идеи для длинных образовательных видео."
            },
            {
              "role": "user", 
              "content": "Создай детальную концепцию для длинного YouTube видео на тему '{{ $json.video_niche }}'. Длительность: {{ $json.target_duration }}.\n\nВыведи результат в JSON формате:\n{\n  \"title\": \"SEO-оптимизированное название\",\n  \"description\": \"Описание видео 150-200 символов\",\n  \"tags\": [\"тег1\", \"тег2\", \"тег3\"],\n  \"sections\": [\n    {\n      \"title\": \"Название раздела\",\n      \"duration\": \"3-4 минуты\",\n      \"key_points\": [\"пункт1\", \"пункт2\"],\n      \"image_prompt\": \"описание для генерации изображения\"\n    }\n  ]\n}"
            }
          ]
        },
        "options": {
          "temperature": 0.7,
          "maxTokens": 3000
        }
      },
      "id": "generate-concept",
      "name": "Генерация концепции",
      "type": "@n8n/n8n-nodes-langchain.openAi",
      "typeVersion": 1.4,
      "position": [
        640,
        300
      ]
    },
    {
      "parameters": {
        "jsCode": "// Парсинг JSON ответа от OpenAI\nconst response = $input.first().json.choices[0].message.content;\n\n// Извлекаем JSON из ответа\nlet conceptData;\ntry {\n  const jsonMatch = response.match(/```json\\n([\\s\\S]*?)\\n```/) || response.match(/{[\\s\\S]*}/);\n  if (jsonMatch) {\n    conceptData = JSON.parse(jsonMatch[1] || jsonMatch[0]);\n  } else {\n    conceptData = JSON.parse(response);\n  }\n} catch (error) {\n  console.log('Ошибка парсинга JSON:', error);\n  conceptData = {\n    title: \"Ошибка генерации названия\",\n    description: \"Ошибка генерации описания\",\n    tags: [\"tech\", \"ai\"],\n    sections: []\n  };\n}\n\n// Подготавливаем данные для дальнейшей обработки\nconst result = {\n  video_title: conceptData.title,\n  video_description: conceptData.description,\n  video_tags: conceptData.tags,\n  sections: conceptData.sections,\n  total_sections: conceptData.sections.length,\n  estimated_duration: conceptData.sections.reduce((acc, section) => {\n    const duration = parseInt(section.duration.match(/\\d+/)[0]) || 3;\n    return acc + duration;\n  }, 0)\n};\n\nconsole.log('Обработанная концепция:', result);\n\nreturn [result];"
      },
      "id": "parse-concept",
      "name": "Обработка концепции",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        840,
        300
      ]
    },
    {
      "parameters": {
        "batchSize": 1,
        "options": {}
      },
      "id": "split-sections",
      "name": "Разделение на секции",
      "type": "n8n-nodes-base.splitInBatches",
      "typeVersion": 3,
      "position": [
        1040,
        300
      ]
    },
    {
      "parameters": {
        "resource": "text",
        "operation": "message",
        "model": "gpt-4o",
        "messages": {
          "messages": [
            {
              "role": "system",
              "content": "Ты - сценарист образовательных YouTube видео. Создаешь детальные сценарии для каждого раздела."
            },
            {
              "role": "user",
              "content": "Создай детальный сценарий для раздела видео:\n\nНазвание: {{ $json.title }}\nДлительность: {{ $json.duration }}\nКлючевые пункты: {{ $json.key_points.join(', ') }}\n\nСценарий должен:\n- Быть написан для озвучки диктором\n- Содержать плавные переходы\n- Включать конкретные факты и примеры\n- Быть увлекательным и информативным\n- Длиться именно {{ $json.duration }}\n\nВерни только готовый текст для озвучки без дополнительных комментариев."
            }
          ]
        },
        "options": {
          "temperature": 0.6,
          "maxTokens": 1500
        }
      },
      "id": "generate-script",
      "name": "Генерация сценария",
      "type": "@n8n/n8n-nodes-langchain.openAi", 
      "typeVersion": 1.4,
      "position": [
        1240,
        200
      ]
    },
    {
      "parameters": {
        "method": "POST",
        "url": "https://cloud-api.leonardo.ai/api/rest/v1/generations",
        "authentication": "genericCredentialType",
        "genericAuthType": "httpHeaderAuth",
        "headers": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "body": {
          "mode": "json",
          "json": {
            "prompt": "{{ $json.image_prompt }}, high quality, professional, 16:9 aspect ratio, modern style",
            "num_images": 2,
            "model_id": "6bef9f1b-29cb-40c7-b9df-32b51c1f67d3",
            "width": 1536,
            "height": 864,
            "guidance_scale": 7
          }
        }
      },
      "id": "generate-images",
      "name": "Генерация изображений",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        1240,
        400
      ]
    },
    {
      "parameters": {
        "method": "POST",
        "url": "https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM",
        "authentication": "genericCredentialType", 
        "genericAuthType": "httpHeaderAuth",
        "headers": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "body": {
          "mode": "json",
          "json": {
            "text": "{{ $json.choices[0].message.content }}",
            "model_id": "eleven_multilingual_v2",
            "voice_settings": {
              "stability": 0.5,
              "similarity_boost": 0.8,
              "style": 0.3,
              "use_speaker_boost": true
            }
          }
        },
        "options": {
          "response": {
            "response": {
              "responseFormat": "file"
            }
          }
        }
      },
      "id": "generate-voiceover",
      "name": "Создание озвучки",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        1440,
        200
      ]
    },
    {
      "parameters": {
        "assignments": {
          "assignments": [
            {
              "id": "script_text",
              "name": "script_text",
              "value": "={{ $('generate-script').item.json.choices[0].message.content }}",
              "type": "string"
            },
            {
              "id": "images_data", 
              "name": "images_data",
              "value": "={{ $('generate-images').item.json }}",
              "type": "object"
            },
            {
              "id": "audio_file",
              "name": "audio_file", 
              "value": "={{ $('generate-voiceover').item.binary }}",
              "type": "object"
            },
            {
              "id": "section_info",
              "name": "section_info",
              "value": "={{ $json }}",
              "type": "object"
            }
          ]
        }
      },
      "id": "combine-section-data",
      "name": "Объединение данных секции",
      "type": "n8n-nodes-base.set",
      "typeVersion": 3.4,
      "position": [
        1640,
        300
      ]
    },
    {
      "parameters": {
        "assignments": {
          "assignments": [
            {
              "id": "all_sections",
              "name": "all_sections", 
              "value": "={{ $input.all().map(item => item.json) }}",
              "type": "array"
            }
          ]
        }
      },
      "id": "collect-all-sections",
      "name": "Сбор всех секций",
      "type": "n8n-nodes-base.set", 
      "typeVersion": 3.4,
      "position": [
        1840,
        300
      ]
    },
    {
      "parameters": {
        "method": "POST",
        "url": "https://api.creatomate.com/v1/renders",
        "authentication": "genericCredentialType",
        "genericAuthType": "httpHeaderAuth", 
        "headers": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "body": {
          "mode": "json",
          "json": {
            "template_id": "YOUR_CREATOMATE_TEMPLATE_ID",
            "modifications": {
              "video-title": "={{ $('parse-concept').item.json.video_title }}",
              "sections": "={{ $json.all_sections }}"
            }
          }
        }
      },
      "id": "render-video",
      "name": "Рендер видео",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        2040,
        300
      ]
    },
    {
      "parameters": {
        "amount": 60,
        "unit": "seconds"
      },
      "id": "wait-for-render",
      "name": "Ожидание рендера",
      "type": "n8n-nodes-base.wait",
      "typeVersion": 1.1,
      "position": [
        2240,
        300
      ]
    },
    {
      "parameters": {
        "resource": "video",
        "operation": "upload",
        "title": "={{ $('parse-concept').item.json.video_title }}",
        "description": "={{ $('parse-concept').item.json.video_description }}",
        "tags": "={{ $('parse-concept').item.json.video_tags }}",
        "categoryId": "28",
        "privacyStatus": "private"
      },
      "id": "upload-to-youtube",
      "name": "Загрузка на YouTube",
      "type": "n8n-nodes-base.youTube",
      "typeVersion": 2,
      "position": [
        2440,
        300
      ]
    },
    {
      "parameters": {
        "assignments": {
          "assignments": [
            {
              "id": "success_message",
              "name": "success_message",
              "value": "🎉 Новое видео создано!\n\n📺 Название: {{ $('parse-concept').item.json.video_title }}\n⏱️ Длительность: ~{{ $('parse-concept').item.json.estimated_duration }} минут\n🔗 Ссылка: https://youtube.com/watch?v={{ $json.id }}\n\n✅ Статус: Готово к публикации",  
              "type": "string"
            }
          ]
        }
      },
      "id": "prepare-notification",
      "name": "Подготовка уведомления",
      "type": "n8n-nodes-base.set",
      "typeVersion": 3.4,
      "position": [
        2640,
        300
      ]
    },
    {
      "parameters": {
        "resource": "message",
        "operation": "sendMessage",
        "chatId": "YOUR_TELEGRAM_CHAT_ID",
        "text": "={{ $json.success_message }}"
      },
      "id": "send-notification",
      "name": "Отправка уведомления",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        2840,
        300
      ]
    }
  ],
  "connections": {
    "Каждые 2 часа": {
      "main": [
        [
          {
            "node": "Конфигурация видео",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Конфигурация видео": {
      "main": [
        [
          {
            "node": "Генерация концепции",
            "type": "main", 
            "index": 0
          }
        ]
      ]
    },
    "Генерация концепции": {
      "main": [
        [
          {
            "node": "Обработка концепции",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Обработка концепции": {
      "main": [
        [
          {
            "node": "Разделение на секции",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Разделение на секции": {
      "main": [
        [
          {
            "node": "Генерация сценария",
            "type": "main",
            "index": 0
          },
          {
            "node": "Генерация изображений", 
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Сбор всех секций",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Генерация сценария": {
      "main": [
        [
          {
            "node": "Создание озвучки",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Создание озвучки": {
      "main": [
        [
          {
            "node": "Объединение данных секции",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Генерация изображений": {
      "main": [
        [
          {
            "node": "Объединение данных секции",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Объединение данных секции": {
      "main": [
        [
          {
            "node": "Разделение на секции",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Сбор всех секций": {
      "main": [
        [
          {
            "node": "Рендер видео",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Рендер видео": {
      "main": [
        [
          {
            "node": "Ожидание рендера",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Ожидание рендера": {
      "main": [
        [
          {
            "node": "Загрузка на YouTube",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Загрузка на YouTube": {
      "main": [
        [
          {
            "node": "Подготовка уведомления",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Подготовка уведомления": {
      "main": [
        [
          {
            "node": "Отправка уведомления",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    
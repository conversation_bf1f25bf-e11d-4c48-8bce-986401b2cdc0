# Автоматизация создания коротких (shorts) YouTube видео в n8n

## 🎯 Цель системы
Полностью автоматизированная система для создания коротких (shorts) YouTube видео (20-60+ секунд) с использованием ИИ, которая работает каждый час без человеческого вмешательства.

## 🏗️ Архитектура системы

### 1. Триггер запуска (Schedule Trigger)
```
- Тип: Cron Schedule
- Время: Каждый час (0 * * * *)
- Альтернатива: Webhook для ручного запуска
```

### 2. Генерация идей и тем (AI Content Planning)
```
Нода: OpenAI / ChatGPT
Промпт: "Сгенерируй идею для длинного YouTube видео на тему [НИША]. 
Создай:
1. Заголовок (SEO-оптимизированный)
2. Описание (150-200 символов)
3. Структуру видео (10-15 разделов)
4. Ключевые слова и теги
5. Таймкоды для каждого раздела"
```

### 3. Создание детального сценария
```
Нода: OpenAI GPT-4
Промпт для каждого раздела:
"Создай детальный сценарий для раздела '[НАЗВАНИЕ_РАЗДЕЛА]' 
длительностью 3-5 минут. Включи:
- Вступительный хук
- Основной контент с фактами
- Переходы между темами
- Призывы к действию"
```

### 4. Генерация изображений для видео
```
Нода: Leonardo AI / Midjourney / DALL-E
Для каждого раздела:
- Создание 3-5 релевантных изображений
- Стиль: Профессиональный, подходящий к теме
- Разрешение: 1920x1080
- Формат: PNG/JPG
```

### 5. Создание озвучки
```
Нода: ElevenLabs / Azure Speech
Параметры:
- Голос: Профессиональный диктор
- Скорость: 0.9x (для лучшего восприятия)
- Эмоции: Увлеченный, но спокойный
- Пауз между разделами: 2 секунды
```

### 6. Сборка видео
```
Нода: Creatomate / Remotion
Шаблон видео:
- Длительность: 2-6 минут
- Фоновая музыка (тихая)
- Переходы между изображениями
- Субтитры (автоматические c алгоритмом определения акцентов на слова усиливая изменением цвета)
- Интро и аутро
- Логотип канала
```

### 7. Загрузка на YouTube
```
Нода: YouTube API
Параметры:
- Статус: Приватный (для проверки)
- Категория: Образование/Развлечения
- Теги: Автоматические из пункта 2
- Плейлист: Автоматически по теме
- Thumbnail: Автоматический из первого изображения
```

### 8. Уведомления и мониторинг
```
Нода: Telegram / Discord / Email
Отправка уведомления с:
- Ссылкой на видео
- Статистикой создания
- Затраченными ресурсами
- Статусом загрузки
```

## 🛠️ Необходимые инструменты и API

### Обязательные сервисы: / локальная альтернатива
1. **OpenAI API** - генерация контента / локальная альтернатива
2. **ElevenLabs API** - озвучка / локальная альтернатива
3. **Leonardo AI / DALL-E** - изображения / локальная альтернатива
4. **Creatomate API** - сборка видео / локальная альтернатива
6. **Google Drive API** - хранение файлов

### Дополнительные сервисы:
1. **Telegram Bot API** - уведомления
2. **Google Sheets API** - трекинг проектов
3. **Airtable API** - база данных контента

## 📋 Пошаговая настройка в n8n

### Шаг 1: Создание нового Workflow
```
1. Открыть n8n
2. Создать новый Workflow
3. Назвать: "YouTube Auto Video Generator"
```

### Шаг 2: Настройка Schedule Trigger
```
1. Добавить нод "Schedule Trigger"
2. Режим: Cron Expression
3. Выражение: 0 */2 * * * (каждые 2 часа)
4. Таймзона: Europe/Moscow
```

### Шаг 3: Настройка генерации идей (локальная альтернатива)
```
1. Добавить нод "OpenAI"    
2. Ресурс: Text
3. Операция: Message a model
4. Модель: gpt-4o
5. Промпт: [См. раздел "Генерация идей"]
6. Температура: 0.7
7. Max Tokens: 2000
```

### Шаг 4: Создание детального сценария
```
1. Добавить нод "Code"
2. Язык: JavaScript
3. Функция: Разбор структуры из предыдущего шага
4. Вывод: Массив разделов для обработки
```

### Шаг 5: Настройка Loop для каждого раздела
```
1. Добавить нод "SplitInBatches"
2. Batch Size: 1
3. Подключить к OpenAI для генерации текста раздела \ локальная альтернатива
4. Подключить к Leonardo AI для изображений \ локальная альтернатива
```

### Шаг 6: Генерация озвучки
```
1. Добавить нод "HTTP Request"
2. URL: ElevenLabs API endpoint
3. Метод: POST
4. Headers: Authorization, Content-Type
5. Body: JSON с текстом и настройками голоса
```

### Шаг 7: Сборка видео в Creatomate
```
1. Добавить нод "HTTP Request" 
2. URL: Creatomate render endpoint
3. Передать:
   - Изображения (URLs)
   - Аудио файлы (URLs)  
   - Титры и субтитры
   - Длительность каждого сегмента
```

### Шаг 8: Загрузка на YouTube
```
1. Добавить нод "Google API"
2. Сервис: YouTube
3. Операция: Upload Video
4. Настроить OAuth 2.0 аутентификацию
5. Файл: Ссылка на готовое видео
6. Метаданные: Название, описание, теги
```

### Шаг 9: Уведомления
```
1. Добавить нод "Telegram"
2. Настроить Bot Token
3. Chat ID для уведомлений
4. Сообщение: Статус создания + ссылка на видео
```

## 💡 Оптимизации и улучшения

### Контроль качества: 
*** реализовать mcp server cline та node с зарпусклом npx ***
- Добавить проверку длительности видео 
- Валидация корректности аудио
- Проверка соответствия изображений тексту

### Экономия ресурсов:
- Кэширование повторяющихся изображений
- Переиспользование аудио заставок
- Оптимизация промптов для снижения токенов

### Масштабирование:
- Множественные ниши контента
- Разные языки озвучки
- Различные стили видео

### Аналитика:
- Трекинг производительности видео
- Анализ популярных тем
- Автоматическая корректировка стратегии

## 🔧 Примерная стоимость запуска

### Месячные расходы (при 12 видео в день): 
- OpenAI API: ~$50-100  \ локальная альтернатива
- ElevenLabs: ~$30-50  \ локальная альтернатива
- Leonardo AI: ~$20-40 \ локальная альтернатива
- Creatomate: ~$25-50 \ локальная альтернатива
- YouTube API: Бесплатно (в лимитах)

**Общий бюджет: $125-240/месяц** \ локальная альтернатива

## ⚠️ Важные моменты

### Правовые аспекты:
- Использовать только royalty-free музыку
- Проверять авторские права на изображения
- Соблюдать политику YouTube

### Технические ограничения:
- Квоты YouTube API (6 загрузок в день по умолчанию)
- Лимиты токенов OpenAI \ локальная альтернатива
- Размер файлов видео

### Рекомендации: 
- Начать с 1-2 видео в день (не актульно, кол-во просмоотров замесяц более 4000000 youtube открыл возмодность подать аявку на монетизацию)
- Тестировать различные ниши 
- Мониторить метрики и оптимизировать

## 🚀 Готовые шаблоны для быстрого старта

В комьюнити n8n доступны готовые шаблоны:
1. "Automated Faceless YouTube Video Generator" (скачать адаптировать)
2. "AI-Powered Long-Form Content Creator"  
3. "YouTube Automation with Multi-Platform Publishing" (скачать адаптировать)

Эти шаблоны можно импортировать и адаптировать под твои потребности!
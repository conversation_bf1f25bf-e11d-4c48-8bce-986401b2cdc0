# app.py - Основной FastAPI сервер для рендеринга видео
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse, JSONResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import uvicorn
import os
import uuid
import asyncio
import json
from datetime import datetime
import logging

from video_processor import VideoProcessor
from audio_generator import AudioGenerator
from image_processor import ImageProcessor

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Video Renderer API",
    description="Бесплатная альтернатива JSON2Video для n8n",
    version="1.0.0"
)

# Глобальные переменные
video_processor = VideoProcessor()
audio_generator = AudioGenerator()
image_processor = ImageProcessor()

# Хранилище заданий (в продакшене используй Redis)
jobs = {}

# Модели данных
class VideoSection(BaseModel):
    text: str
    image_url: Optional[str] = None
    image_base64: Optional[str] = None
    duration: float = 5.0
    transition: str = "fade"

class VideoStyle(BaseModel):
    resolution: str = "1920x1080"
    fps: int = 30
    background_music: Optional[str] = None
    voice_model: str = "default"
    font_family: str = "Arial"
    font_size: int = 48
    subtitle_position: str = "bottom"

class VideoRenderRequest(BaseModel):
    title: str
    sections: List[VideoSection]
    style: VideoStyle = VideoStyle()
    webhook_url: Optional[str] = None
    claraverse_integration: bool = False

class JobStatus(BaseModel):
    job_id: str
    status: str  # pending, processing, completed, failed
    progress: float = 0.0
    video_url: Optional[str] = None
    error_message: Optional[str] = None
    created_at: datetime
    completed_at: Optional[datetime] = None

@app.get("/")
async def root():
    return {
        "message": "Video Renderer API готов к работе!",
        "endpoints": {
            "render": "POST /render",
            "status": "GET /status/{job_id}",
            "download": "GET /download/{video_id}",
            "health": "GET /health"
        }
    }

@app.get("/health")
async def health_check():
    """Проверка работоспособности API"""
    try:
        # Проверяем доступность FFmpeg
        import subprocess
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=5)
        ffmpeg_available = result.returncode == 0
        
        return {
            "status": "healthy",
            "ffmpeg_available": ffmpeg_available,
            "active_jobs": len([j for j in jobs.values() if j["status"] == "processing"]),
            "total_jobs": len(jobs)
        }
    except Exception as e:
        return {
            "status": "unhealthy", 
            "error": str(e)
        }

@app.post("/render", response_model=Dict[str, str])
async def render_video(
    request: VideoRenderRequest, 
    background_tasks: BackgroundTasks
):
    """Запуск рендеринга видео"""
    try:
        job_id = str(uuid.uuid4())
        
        # Создаем запись о задании
        jobs[job_id] = {
            "job_id": job_id,
            "status": "pending",
            "progress": 0.0,
            "video_url": None,
            "error_message": None,
            "created_at": datetime.now(),
            "completed_at": None,
            "request_data": request.dict()
        }
        
        # Запускаем рендеринг в фоне
        background_tasks.add_task(process_video_job, job_id, request)
        
        logger.info(f"Создано задание рендеринга: {job_id}")
        
        return {
            "job_id": job_id,
            "status": "pending",
            "message": "Рендеринг видео запущен",
            "status_url": f"/status/{job_id}"
        }
        
    except Exception as e:
        logger.error(f"Ошибка создания задания: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Ошибка запуска рендеринга: {str(e)}")

@app.get("/status/{job_id}", response_model=JobStatus)
async def get_job_status(job_id: str):
    """Получение статуса задания"""
    if job_id not in jobs:
        raise HTTPException(status_code=404, detail="Задание не найдено")
    
    job = jobs[job_id]
    return JobStatus(**job)

@app.get("/download/{video_id}")
async def download_video(video_id: str):
    """Скачивание готового видео"""
    video_path = f"output/{video_id}.mp4"
    
    if not os.path.exists(video_path):
        raise HTTPException(status_code=404, detail="Видео не найдено")
    
    return FileResponse(
        video_path,
        media_type="video/mp4",
        filename=f"{video_id}.mp4"
    )

@app.post("/webhook/claraverse")
async def claraverse_webhook(data: Dict[str, Any]):
    """Webhook для интеграции с ClaraVerse"""
    try:
        logger.info(f"Получен webhook от ClaraVerse: {data}")
        
        # Здесь можно добавить логику обработки данных от ClaraVerse
        # Например, автоматический запуск рендеринга на основе событий
        
        if "trigger_render" in data and data["trigger_render"]:
            # Создаем запрос на рендеринг из данных ClaraVerse
            render_request = VideoRenderRequest(
                title=data.get("title", "Auto-generated video"),
                sections=[
                    VideoSection(
                        text=section["text"],
                        image_url=section.get("image_url"),
                        duration=section.get("duration", 5.0)
                    ) for section in data.get("sections", [])
                ],
                claraverse_integration=True
            )
            
            # Запускаем рендеринг
            job_id = str(uuid.uuid4())
            background_tasks = BackgroundTasks()
            background_tasks.add_task(process_video_job, job_id, render_request)
            
            return {"status": "success", "job_id": job_id}
        
        return {"status": "received"}
        
    except Exception as e:
        logger.error(f"Ошибка обработки webhook: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

async def process_video_job(job_id: str, request: VideoRenderRequest):
    """Фоновая обработка задания рендеринга"""
    try:
        # Обновляем статус
        jobs[job_id]["status"] = "processing"
        jobs[job_id]["progress"] = 10.0
        
        logger.info(f"Начинаем обработку задания {job_id}")
        
        # 1. Обработка аудио
        jobs[job_id]["progress"] = 20.0
        audio_files = []
        
        for i, section in enumerate(request.sections):
            logger.info(f"Генерируем аудио для секции {i+1}")
            audio_file = await audio_generator.generate_audio(
                text=section.text,
                voice_model=request.style.voice_model,
                output_path=f"temp/{job_id}_audio_{i}.wav"
            )
            audio_files.append(audio_file)
        
        jobs[job_id]["progress"] = 40.0
        
        # 2. Обработка изображений
        image_files = []
        
        for i, section in enumerate(request.sections):
            logger.info(f"Обрабатываем изображение для секции {i+1}")
            
            if section.image_url:
                image_file = await image_processor.download_and_process_image(
                    url=section.image_url,
                    output_path=f"temp/{job_id}_image_{i}.jpg",
                    resolution=request.style.resolution
                )
            elif section.image_base64:
                image_file = await image_processor.process_base64_image(
                    base64_data=section.image_base64,
                    output_path=f"temp/{job_id}_image_{i}.jpg",
                    resolution=request.style.resolution
                )
            else:
                # Генерируем простое изображение с текстом
                image_file = await image_processor.generate_text_image(
                    text=section.text[:100] + "...",
                    output_path=f"temp/{job_id}_image_{i}.jpg",
                    resolution=request.style.resolution
                )
            
            image_files.append(image_file)
        
        jobs[job_id]["progress"] = 60.0
        
        # 3. Сборка видео
        logger.info(f"Собираем финальное видео для {job_id}")
        
        video_path = await video_processor.create_video(
            job_id=job_id,
            sections=request.sections,
            audio_files=audio_files,
            image_files=image_files,
            style=request.style,
            output_path=f"output/{job_id}.mp4"
        )
        
        jobs[job_id]["progress"] = 90.0
        
        # 4. Финализация
        jobs[job_id]["status"] = "completed"
        jobs[job_id]["progress"] = 100.0
        jobs[job_id]["video_url"] = f"/download/{job_id}"
        jobs[job_id]["completed_at"] = datetime.now()
        
        logger.info(f"Задание {job_id} успешно завершено")
        
        # Отправляем webhook если указан
        if request.webhook_url:
            await send_webhook_notification(request.webhook_url, jobs[job_id])
        
        # Интеграция с ClaraVerse
        if request.claraverse_integration:
            await notify_claraverse(jobs[job_id])
        
        # Очистка временных файлов
        cleanup_temp_files(job_id)
        
    except Exception as e:
        logger.error(f"Ошибка при обработке задания {job_id}: {str(e)}")
        jobs[job_id]["status"] = "failed"
        jobs[job_id]["error_message"] = str(e)
        jobs[job_id]["completed_at"] = datetime.now()

async def send_webhook_notification(webhook_url: str, job_data: dict):
    """Отправка уведомления на webhook"""
    try:
        import aiohttp
        async with aiohttp.ClientSession() as session:
            async with session.post(webhook_url, json=job_data) as response:
                logger.info(f"Webhook отправлен: {response.status}")
    except Exception as e:
        logger.error(f"Ошибка отправки webhook: {str(e)}")

async def notify_claraverse(job_data: dict):
    """Уведомление ClaraVerse о завершении"""
    try:
        # Здесь добавляется логика уведомления ClaraVerse
        logger.info(f"Уведомляем ClaraVerse о завершении: {job_data['job_id']}")
    except Exception as e:
        logger.error(f"Ошибка уведомления ClaraVerse: {str(e)}")

def cleanup_temp_files(job_id: str):
    """Очистка временных файлов"""
    try:
        import glob
        temp_files = glob.glob(f"temp/{job_id}_*")
        for file in temp_files:
            os.remove(file)
        logger.info(f"Временные файлы для {job_id} очищены")
    except Exception as e:
        logger.error(f"Ошибка очистки файлов: {str(e)}")

# Создание необходимых директорий
os.makedirs("temp", exist_ok=True)
os.makedirs("output", exist_ok=True)
os.makedirs("templates", exist_ok=True)

if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
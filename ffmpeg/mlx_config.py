# Environment Configuration - MLX Local System
# All external API references have been removed for privacy

# === MLX MODEL PATHS (LOCAL ONLY) ===
MLX_MODELS_PATH = "/Volumes/SAB500/VideoLingo-MLX-Project/models"
QWEN_MODEL_PATH = f"{MLX_MODELS_PATH}/qwen2-vl-2b-instruct-4bit"
PALIGEMMA_MODEL_PATH = f"{MLX_MODELS_PATH}/paligemma-3b-mix-448-8bit"
WHISPER_MODEL_PATH = f"{MLX_MODELS_PATH}/whisper-large-v3-mlx"
COMFYUI_PATH = f"{MLX_MODELS_PATH}/comfyui-mlx"

# === LOCAL PROCESSING SETTINGS ===
LOCAL_ENGINE_PORT = 8000
LOCAL_ENGINE_HOST = "localhost"
PROCESSING_TEMP_DIR = "/tmp/mlx_video_processing"
OUTPUT_DIR = "/Volumes/SAB500/VideoLingo-MLX-Project/output"

# === VIDEO GENERATION SETTINGS ===
DEFAULT_RESOLUTION = "1920x1080"
DEFAULT_FPS = 30
DEFAULT_FORMAT = "mp4"
SHORTS_RESOLUTION = "1080x1920"

# === CONTENT CONFIGURATION ===
CONTENT_THEMES = {
    "philosophy": "Ancient wisdom through nature metaphors",
    "stoicism": "Life lessons from animal behavior", 
    "mindfulness": "Present moment awareness through natural examples"
}

VOICE_PROFILES = {
    "calm_narrator": {
        "speed": 0.9,
        "tone": "peaceful",
        "emotion": "serene"
    },
    "wise_teacher": {
        "speed": 0.8,
        "tone": "authoritative",
        "emotion": "knowledgeable"
    }
}

# === MULTILINGUAL SETTINGS ===
SUPPORTED_LANGUAGES = ["en", "ru", "es", "fr", "de", "hi", "zh"]
DEFAULT_SUBTITLE_STYLE = "minimal, bottom-center, transparent background"

# === AUTOMATION SETTINGS ===
GENERATION_SCHEDULE = "0 */2 * * *"  # Every 2 hours
BATCH_SIZE = 5  # Number of videos per batch
AUTO_PUBLISH = True
QUALITY_CHECK_ENABLED = True

# === SYSTEM MONITORING ===
LOG_LEVEL = "INFO"
PERFORMANCE_MONITORING = True
STORAGE_CLEANUP_ENABLED = True
TEMP_FILE_RETENTION_HOURS = 2

# === PRIVACY & SECURITY ===
EXTERNAL_API_DISABLED = True
LOCAL_PROCESSING_ONLY = True
DATA_RETENTION_DAYS = 30
ANALYTICS_TRACKING = False

# === N8N INTEGRATION ===
N8N_WEBHOOK_ENABLED = True
N8N_SUCCESS_WEBHOOK = "http://localhost:5678/webhook/video-completed"
N8N_ERROR_WEBHOOK = "http://localhost:5678/webhook/video-failed"

print("🔒 MLX Local Configuration Loaded - 100% Privacy Mode Active")
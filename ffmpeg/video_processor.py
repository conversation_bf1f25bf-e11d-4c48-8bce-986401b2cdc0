# video_processor.py - Обработка видео с помощью FFmpeg
import subprocess
import os
import asyncio
import logging
from typing import List
from pydantic import BaseModel

logger = logging.getLogger(__name__)

class VideoProcessor:
    def __init__(self):
        self.temp_dir = "temp"
        self.output_dir = "output"
        
    async def create_video(
        self, 
        job_id: str,
        sections: List,
        audio_files: List[str],
        image_files: List[str],
        style,
        output_path: str
    ) -> str:
        """Создание финального видео из компонентов"""
        try:
            logger.info(f"Начинаем сборку видео для job {job_id}")
            
            # Получаем разрешение
            width, height = map(int, style.resolution.split('x'))
            fps = style.fps
            
            # 1. Создаем видео-сегменты для каждой секции
            video_segments = []
            
            for i, (section, audio_file, image_file) in enumerate(zip(sections, audio_files, image_files)):
                segment_path = f"{self.temp_dir}/{job_id}_segment_{i}.mp4"
                
                # Получаем длительность аудио
                audio_duration = await self._get_audio_duration(audio_file)
                
                # Создаем видео сегмент
                await self._create_video_segment(
                    image_file=image_file,
                    audio_file=audio_file,
                    duration=audio_duration,
                    output_path=segment_path,
                    width=width,
                    height=height,
                    fps=fps,
                    transition=section.transition
                )
                
                video_segments.append(segment_path)
                logger.info(f"Создан сегмент {i+1}: {segment_path}")
            
            # 2. Объединяем все сегменты
            if len(video_segments) == 1:
                # Если только один сегмент, просто копируем
                await self._copy_file(video_segments[0], output_path)
            else:
                # Объединяем несколько сегментов
                await self._concatenate_videos(video_segments, output_path)
            
            # 3. Добавляем фоновую музыку если указана
            if style.background_music:
                await self._add_background_music(output_path, style.background_music)
            
            # 4. Добавляем субтитры если нужно
            if hasattr(style, 'add_subtitles') and style.add_subtitles:
                await self._add_subtitles(output_path, sections)
            
            logger.info(f"Видео успешно создано: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Ошибка создания видео: {str(e)}")
            raise
    
    async def _create_video_segment(
        self,
        image_file: str,
        audio_file: str,
        duration: float,
        output_path: str,
        width: int,
        height: int,
        fps: int,
        transition: str = "fade"
    ):
        """Создание одного видео сегмента"""
        try:
            # Базовая FFmpeg команда для создания видео из изображения и аудио
            cmd = [
                'ffmpeg',
                '-y',  # Перезаписывать выходные файлы
                '-loop', '1',  # Зацикливать изображение
                '-i', image_file,  # Входное изображение
                '-i', audio_file,  # Входное аудио
                '-c:v', 'libx264',  # Видео кодек
                '-c:a', 'aac',  # Аудио кодек
                '-b:a', '128k',  # Битрейт аудио
                '-pix_fmt', 'yuv420p',  # Формат пикселей
                '-vf', f'scale={width}:{height}:force_original_aspect_ratio=decrease,pad={width}:{height}:(ow-iw)/2:(oh-ih)/2,fps={fps}',
                '-shortest',  # Остановить когда кончится самый короткий поток
                '-t', str(duration),  # Длительность
                output_path
            ]
            
            # Добавляем эффекты перехода
            if transition == "fade":
                # Добавляем fade in/out
                fade_duration = min(0.5, duration / 4)  # Максимум 0.5 сек или 1/4 от общей длительности
                cmd[cmd.index('-vf')+1] += f',fade=in:0:{int(fade_duration*fps)},fade=out:{int((duration-fade_duration)*fps)}:{int(fade_duration*fps)}'
            
            logger.info(f"Выполняем FFmpeg команду: {' '.join(cmd)}")
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode() if stderr else "Неизвестная ошибка FFmpeg"
                logger.error(f"FFmpeg ошибка: {error_msg}")
                raise Exception(f"Ошибка создания видео сегмента: {error_msg}")
            
            logger.info(f"Сегмент создан успешно: {output_path}")
            
        except Exception as e:
            logger.error(f"Ошибка создания сегмента: {str(e)}")
            raise
    
    async def _get_audio_duration(self, audio_file: str) -> float:
        """Получение длительности аудио файла"""
        try:
            cmd = [
                'ffprobe',
                '-v', 'quiet',
                '-show_entries', 'format=duration',
                '-of', 'csv=p=0',
                audio_file
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.warning(f"Не удалось получить длительность аудио, используем значение по умолчанию")
                return 5.0  # Значение по умолчанию
            
            duration = float(stdout.decode().strip())
            logger.info(f"Длительность аудио: {duration} сек")
            return duration
            
        except Exception as e:
            logger.warning(f"Ошибка получения длительности аудио: {str(e)}")
            return 5.0  # Значение по умолчанию
    
    async def _concatenate_videos(self, video_files: List[str], output_path: str):
        """Объединение нескольких видео файлов"""
        try:
            # Создаем список файлов для concat
            concat_file = f"{self.temp_dir}/concat_list_{os.path.basename(output_path)}.txt"
            
            with open(concat_file, 'w') as f:
                for video_file in video_files:
                    f.write(f"file '{os.path.abspath(video_file)}'\n")
            
            cmd = [
                'ffmpeg',
                '-y',
                '-f', 'concat',
                '-safe', '0',
                '-i', concat_file,
                '-c', 'copy',  # Копируем без перекодирования
                output_path
            ]
            
            logger.info(f"Объединяем видео: {' '.join(cmd)}")
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode() if stderr else "Неизвестная ошибка FFmpeg"
                logger.error(f"Ошибка объединения видео: {error_msg}")
                raise Exception(f"Ошибка объединения видео: {error_msg}")
            
            # Удаляем временный файл списка
            os.remove(concat_file)
            
            logger.info(f"Видео успешно объединено: {output_path}")
            
        except Exception as e:
            logger.error(f"Ошибка объединения видео: {str(e)}")
            raise
    
    async def _add_background_music(self, video_path: str, music_path: str):
        """Добавление фоновой музыки к видео"""
        try:
            temp_output = f"{video_path}_temp.mp4"
            
            cmd = [
                'ffmpeg',
                '-y',
                '-i', video_path,
                '-i', music_path,
                '-filter_complex', '[1:a]volume=0.3[a1];[0:a][a1]amix=inputs=2:duration=shortest[a]',
                '-map', '0:v',
                '-map', '[a]',
                '-c:v', 'copy',
                '-c:a', 'aac',
                temp_output
            ]
            
            logger.info(f"Добавляем фоновую музыку: {music_path}")
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode() if stderr else "Ошибка добавления музыки"
                logger.warning(f"Не удалось добавить музыку: {error_msg}")
                return  # Не критическая ошибка
            
            # Заменяем оригинальный файл
            os.replace(temp_output, video_path)
            logger.info("
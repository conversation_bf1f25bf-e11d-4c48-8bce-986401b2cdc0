{"mcpServers": {"mcp-installer": {"command": "npx", "args": ["@anaisbetts/mcp-installer"], "env": {}}, "whois-mcp": {"command": "node", "args": ["/Users/<USER>/whois-mcp/dist/index.js"], "env": {}}, "context7-mcp": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@upstash/context7-mcp", "--key", "7122bec7-4656-4efe-a4a2-12921a055ec6"], "env": {}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}}, "whatsapp": {"command": "/usr/local/bin/uv", "args": ["--directory", "/Volumes/DATA/whatsapp-mcp/whatsapp-mcp-server", "run", "main.py"], "env": {}}, "desktop-commander": {"command": "npx", "args": ["@wonderwhy-er/desktop-commander@latest"]}, "servers": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@jlia0/servers", "--key", "b523f26f-db45-4d27-bbda-225aa68a616e", "--profile", "salty-wildfowl-03J83F"], "env": {}}, "toolbox": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@smithery/toolbox", "--key", "7122bec7-4656-4efe-a4a2-12921a055ec6", "--profile", "renewed-whale-FrWczB"], "env": {}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Downloads", "/Volumes", "/Volumes/DATA", "/Users/<USER>/Projects/MCP_SERVS", "/Volumes/SAB500/our_mcp_server_client", "/Volumes/SAB500/VideoLingo", "/Users/<USER>/.codeium/windsurf", "/Users/<USER>/Projects/MCP_SERVS/memory", "/Volumes/DATA2/cua-sharing-automation/mcp-servers/task-manager"], "env": {}}, "mcp-shrimp-task-manager": {"command": "node", "args": ["/Users/<USER>/Projects/MCP_SERVS/mcp-shrimp-task-manager/dist/index.js"], "env": {"DATA_DIR": "/Volumes/SAB500/VideoLingo", "TEMPLATES_USE": "en", "ENABLE_GUI": "false", "HOST": "**************", "PORT": "3007"}}, "ika-compressor-mcp": {"command": "npx", "args": ["-y", "/Users/<USER>/Projects/MCP_SERVS/ika-compressor-mcp"], "env": {"PYTHON_PATH": "python3"}}, "youtube-analytics-stable": {"command": "node", "args": ["/Users/<USER>/mcp-disk/MCP_SERV/youtube-analytics/index.js"], "env": {}}, "mcp-browser": {"command": "uvx", "args": ["mcp-browser"], "env": {}}}}
{"name": "YouTube Video Automation - MLX Local Engine", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 */2 * * *"}]}}, "id": "schedule-trigger", "name": "Automation Timer", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [240, 300]}, {"parameters": {"assignments": {"assignments": [{"id": "video_niche", "name": "video_niche", "value": "philosophy through nature", "type": "string"}, {"id": "target_format", "name": "target_format", "value": "short-form wisdom", "type": "string"}, {"id": "content_style", "name": "content_style", "value": "animal metaphors, life lessons", "type": "string"}]}}, "id": "set-config", "name": "Content Configuration", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [440, 300]}, {"parameters": {"method": "POST", "url": "http://localhost:8000/mlx/generate-concept", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "body": {"mode": "json", "json": {"niche": "{{ $json.video_niche }}", "format": "{{ $json.target_format }}", "style": "{{ $json.content_style }}", "count": 5}}}, "id": "generate-concept", "name": "MLX Concept Generator", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [640, 300]}, {"parameters": {"jsCode": "// Process MLX response\nconst response = $input.first().json;\nlet conceptData;\n\ntry {\n  conceptData = response.concepts || response;\n} catch (error) {\n  console.log('Processing error:', error);\n  conceptData = {\n    title: \"Wisdom from Nature\",\n    sections: [{\n      title: \"Patient Turtle\",\n      text: \"Like the wise turtle, true progress comes from steady persistence.\",\n      image_prompt: \"wise old turtle by peaceful pond\"\n    }]\n  };\n}\n\nconst result = {\n  video_title: conceptData.title,\n  sections: conceptData.sections,\n  total_sections: conceptData.sections?.length || 1\n};\n\nreturn [result];"}, "id": "parse-concept", "name": "Process Concepts", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [840, 300]}, {"parameters": {"batchSize": 1, "options": {}}, "id": "split-sections", "name": "Section Processor", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1040, 300]}, {"parameters": {"method": "POST", "url": "http://localhost:8000/mlx/generate-script", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "body": {"mode": "json", "json": {"title": "{{ $json.title }}", "content": "{{ $json.text }}", "style": "narrative, calm, philosophical"}}}, "id": "generate-script", "name": "MLX Script Writer", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1240, 200]}, {"parameters": {"method": "POST", "url": "http://localhost:8000/mlx/generate-image", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "body": {"mode": "json", "json": {"prompt": "{{ $json.image_prompt }}, serene, philosophical, high quality, 16:9 aspect ratio", "style": "realistic, peaceful, nature-focused", "resolution": "1536x864"}}}, "id": "generate-images", "name": "MLX Image Generator", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1240, 400]}, {"parameters": {"method": "POST", "url": "http://localhost:8000/mlx/generate-voice", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "body": {"mode": "json", "json": {"text": "{{ $('generate-script').item.json.script }}", "voice_profile": "calm_narrator", "speed": 0.9, "emotion": "peaceful"}}, "options": {"response": {"response": {"responseFormat": "file"}}}}, "id": "generate-voiceover", "name": "MLX Voice Synthesis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1440, 200]}, {"parameters": {"assignments": {"assignments": [{"id": "script_text", "name": "script_text", "value": "={{ $('generate-script').item.json.script }}", "type": "string"}, {"id": "images_data", "name": "images_data", "value": "={{ $('generate-images').item.json }}", "type": "object"}, {"id": "audio_file", "name": "audio_file", "value": "={{ $('generate-voiceover').item.binary }}", "type": "object"}, {"id": "section_info", "name": "section_info", "value": "={{ $json }}", "type": "object"}]}}, "id": "combine-section-data", "name": "Combine Section Assets", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1640, 300]}, {"parameters": {"assignments": {"assignments": [{"id": "all_sections", "name": "all_sections", "value": "={{ $input.all().map(item => item.json) }}", "type": "array"}]}}, "id": "collect-all-sections", "name": "Asset Collection", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1840, 300]}, {"parameters": {"method": "POST", "url": "http://localhost:8000/video/render", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "body": {"mode": "json", "json": {"title": "={{ $('parse-concept').item.json.video_title }}", "sections": "={{ $json.all_sections }}", "style": {"resolution": "1920x1080", "fps": 30, "format": "shorts", "transitions": "fade"}}}}, "id": "render-video", "name": "Local Video Renderer", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2040, 300]}, {"parameters": {"amount": 30, "unit": "seconds"}, "id": "wait-for-render", "name": "Processing Wait", "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [2240, 300]}, {"parameters": {"method": "POST", "url": "http://localhost:8000/video/add-subtitles", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "body": {"mode": "json", "json": {"video_id": "={{ $('render-video').item.json.job_id }}", "languages": ["en", "ru", "es", "fr", "de"], "style": "minimal, bottom-center"}}}, "id": "add-subtitles", "name": "MLX Subtitle Generator", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2440, 300]}, {"parameters": {"resource": "video", "operation": "upload", "title": "={{ $('parse-concept').item.json.video_title }}", "description": "Wisdom through nature. Daily philosophy for mindful living. #philosophy #wisdom #nature #mindfulness #stoicism", "categoryId": "22", "privacyStatus": "public"}, "id": "upload-to-youtube", "name": "YouTube Publisher", "type": "n8n-nodes-base.youTube", "typeVersion": 2, "position": [2640, 300]}, {"parameters": {"assignments": {"assignments": [{"id": "success_message", "name": "success_message", "value": "🌿 New wisdom video published!\n\n📺 Title: {{ $('parse-concept').item.json.video_title }}\n🎬 Format: Philosophical Short\n🔗 Link: https://youtube.com/watch?v={{ $json.id }}\n\n✅ Status: Live with multilingual subtitles", "type": "string"}]}}, "id": "prepare-notification", "name": "Success Notification", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2840, 300]}, {"parameters": {"resource": "message", "operation": "sendMessage", "chatId": "YOUR_TELEGRAM_CHAT_ID", "text": "={{ $json.success_message }}"}, "id": "send-notification", "name": "Notify Success", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [3040, 300]}], "connections": {"Automation Timer": {"main": [[{"node": "Content Configuration", "type": "main", "index": 0}]]}, "Content Configuration": {"main": [[{"node": "MLX Concept Generator", "type": "main", "index": 0}]]}, "MLX Concept Generator": {"main": [[{"node": "Process Concepts", "type": "main", "index": 0}]]}, "Process Concepts": {"main": [[{"node": "Section Processor", "type": "main", "index": 0}]]}, "Section Processor": {"main": [[{"node": "MLX Script Writer", "type": "main", "index": 0}, {"node": "MLX Image Generator", "type": "main", "index": 0}], [{"node": "Asset Collection", "type": "main", "index": 0}]]}, "MLX Script Writer": {"main": [[{"node": "MLX Voice Synthesis", "type": "main", "index": 0}]]}, "MLX Voice Synthesis": {"main": [[{"node": "Combine Section Assets", "type": "main", "index": 0}]]}, "MLX Image Generator": {"main": [[{"node": "Combine Section Assets", "type": "main", "index": 0}]]}, "Combine Section Assets": {"main": [[{"node": "Section Processor", "type": "main", "index": 0}]]}, "Asset Collection": {"main": [[{"node": "Local Video Renderer", "type": "main", "index": 0}]]}, "Local Video Renderer": {"main": [[{"node": "Processing Wait", "type": "main", "index": 0}]]}, "Processing Wait": {"main": [[{"node": "MLX Subtitle Generator", "type": "main", "index": 0}]]}, "MLX Subtitle Generator": {"main": [[{"node": "YouTube Publisher", "type": "main", "index": 0}]]}, "YouTube Publisher": {"main": [[{"node": "Success Notification", "type": "main", "index": 0}]]}, "Success Notification": {"main": [[{"node": "Notify Success", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"timezone": "UTC", "saveManualExecutions": true}}
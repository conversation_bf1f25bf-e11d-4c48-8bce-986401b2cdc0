{"name": "VideoLingo MLX - YouTube & Telegram Integration", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 */2 * * *"}]}}, "id": "schedule-trigger", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [240, 300]}, {"parameters": {"assignments": {"assignments": [{"id": "starting_idea", "name": "starting_idea", "value": "Wisdom through nature - daily philosophy for mindful living", "type": "string"}, {"id": "video_style", "name": "video_style", "value": "philosophical, calming, nature-focused", "type": "string"}, {"id": "content_type", "name": "content_type", "value": "shorts", "type": "string"}, {"id": "youtube_api_key", "name": "youtube_api_key", "value": "AIzaSyAeI-U4Et6xrWx4IX5Hol14eZyJAk0UGfU", "type": "string"}]}}, "id": "input-config", "name": "Configuration", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [440, 300]}, {"parameters": {"method": "POST", "url": "http://localhost:8000/mlx/generate-concept", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "body": {"mode": "json", "json": {"theme": "={{ $json.starting_idea }}", "style": "={{ $json.video_style }}", "content_type": "={{ $json.content_type }}", "sections_count": 4}}}, "id": "generate-concept", "name": "Generate Video Concept", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [640, 300]}, {"parameters": {"batchSize": 1, "options": {}}, "id": "split-sections", "name": "Process Each Section", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [840, 300]}, {"parameters": {"method": "POST", "url": "http://localhost:8000/mlx/generate-image", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "body": {"mode": "json", "json": {"prompt": "={{ $json.image_prompt }}, serene, philosophical, high quality, 16:9 aspect ratio, peaceful nature scene", "style": "realistic, calming, professional", "resolution": "1920x1080"}}}, "id": "generate-images", "name": "Generate Images", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1040, 400]}, {"parameters": {"method": "POST", "url": "http://localhost:8000/mlx/generate-voice", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "body": {"mode": "json", "json": {"text": "={{ $json.text }}", "voice_profile": "calm_narrator", "speed": 0.9, "emotion": "peaceful"}}}, "id": "generate-voice", "name": "Generate Voice", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1040, 200]}, {"parameters": {"assignments": {"assignments": [{"id": "section_data", "name": "section_data", "value": "={{ { text: $json.text, image_url: $('generate-images').item.json.image_url, audio_data: $('generate-voice').item.json.audio_data, duration: $json.duration || 5 } }}", "type": "object"}]}}, "id": "combine-assets", "name": "Combine Section Assets", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1240, 300]}, {"parameters": {"assignments": {"assignments": [{"id": "all_sections", "name": "all_sections", "value": "={{ $input.all().map(item => item.json.section_data) }}", "type": "array"}]}}, "id": "collect-sections", "name": "Collect All Sections", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1440, 300]}, {"parameters": {"method": "POST", "url": "http://localhost:8000/video/render", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "body": {"mode": "json", "json": {"title": "={{ $('generate-concept').item.json.concepts.title }}", "sections": "={{ $json.all_sections }}", "style": {"resolution": "1080x1920", "fps": 30, "format": "shorts", "transitions": "fade", "background_music": true}}}}, "id": "render-video", "name": "Render Final Video", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1640, 300]}, {"parameters": {"amount": 45, "unit": "seconds"}, "id": "wait-processing", "name": "Wait for Video Processing", "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1840, 300]}, {"parameters": {"method": "POST", "url": "http://localhost:8000/video/add-subtitles", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "body": {"mode": "json", "json": {"video_id": "={{ $('render-video').item.json.job_id }}", "languages": ["en", "ru"], "style": "minimal, bottom-center"}}}, "id": "add-subtitles", "name": "Add Subtitles", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2040, 300]}, {"parameters": {"assignments": {"assignments": [{"id": "video_file_path", "name": "video_file_path", "value": "=/tmp/videos/={{ $('render-video').item.json.job_id }}.mp4", "type": "string"}, {"id": "video_title", "name": "video_title", "value": "={{ $('generate-concept').item.json.concepts.title }} | @GodlySharing", "type": "string"}, {"id": "video_description", "name": "video_description", "value": "🌿 {{ $('generate-concept').item.json.concepts.description || 'Wisdom through nature - daily philosophy for mindful living' }}\n\n✨ Follow @GodlySharing for daily inspiration\n🎯 Subscribe for more philosophical content\n\n#Philosophy #Wisdom #Nature #Mindfulness #Inspiration #Shorts #GodlySharing #DailyWisdom #Motivation #LifeLessons", "type": "string"}, {"id": "video_tags", "name": "video_tags", "value": "philosophy,wisdom,nature,mindfulness,inspiration,shorts,godlysharing,daily,motivation,life lessons,spiritual,peaceful,meditation,growth", "type": "string"}]}}, "id": "prepare-youtube-upload", "name": "Prepare YouTube Upload", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2240, 300]}, {"parameters": {"method": "POST", "url": "https://www.googleapis.com/upload/youtube/v3/videos?part=snippet,status&key={{ $('input-config').item.json.youtube_api_key }}", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "body": {"mode": "json", "json": {"snippet": {"title": "={{ $json.video_title }}", "description": "={{ $json.video_description }}", "tags": "={{ $json.video_tags.split(',') }}", "categoryId": "22", "defaultLanguage": "en", "defaultAudioLanguage": "en"}, "status": {"privacyStatus": "public", "selfDeclaredMadeForKids": false}}}}, "id": "upload-youtube", "name": "Upload to YouTube", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2440, 300]}, {"parameters": {"chatId": "@GodlySharing", "text": "🎬 Новое видео успешно загружено на YouTube!\n\n📹 Название: {{ $('prepare-youtube-upload').item.json.video_title }}\n🔗 Ссылка: https://youtu.be/{{ $('upload-youtube').item.json.id }}\n\n✨ Автоматически создано через MLX VideoLingo\n🤖 Локальная обработка без внешних API\n📊 Канал: @GodlySharing\n\n#VideoLingo #MLX #AutomatedContent", "additionalFields": {"parse_mode": "HTML", "disable_web_page_preview": false}}, "id": "telegram-success-notification", "name": "Telegram Success Notification", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [2640, 200], "credentials": {"telegramApi": {"id": "telegram-godly-bot", "name": "Telegram GodlySharing Bot"}}}, {"parameters": {"chatId": "@GodlySharing", "text": "⚠️ Ошибка при создании видео!\n\n🔴 Процесс: {{ $json.error_stage || 'Unknown' }}\n❌ Ошибка: {{ $json.error_message || 'Process failed' }}\n⏰ Время: {{ new Date().toLocaleString('ru-RU') }}\n\n🔧 Требуется ручная проверка MLX VideoLingo\n🤖 @GodlySharingBot", "additionalFields": {"parse_mode": "HTML"}}, "id": "telegram-error-notification", "name": "Telegram Error Notification", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [2640, 400], "credentials": {"telegramApi": {"id": "telegram-godly-bot", "name": "Telegram GodlySharing Bot"}}}, {"parameters": {"assignments": {"assignments": [{"id": "youtube_video_id", "name": "youtube_video_id", "value": "={{ $('upload-youtube').item.json.id }}", "type": "string"}, {"id": "youtube_url", "name": "youtube_url", "value": "https://youtu.be/={{ $('upload-youtube').item.json.id }}", "type": "string"}, {"id": "process_completed", "name": "process_completed", "value": true, "type": "boolean"}, {"id": "completion_time", "name": "completion_time", "value": "={{ new Date().toISOString() }}", "type": "string"}, {"id": "video_stats", "name": "video_stats", "value": "={{ { title: $('prepare-youtube-upload').item.json.video_title, duration: $('render-video').item.json.duration, size: $('render-video').item.json.file_size } }}", "type": "object"}]}}, "id": "final-output", "name": "Final Output", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2840, 300]}], "connections": {"Schedule Trigger": {"main": [[{"node": "Configuration", "type": "main", "index": 0}]]}, "Configuration": {"main": [[{"node": "Generate Video Concept", "type": "main", "index": 0}]]}, "Generate Video Concept": {"main": [[{"node": "Process Each Section", "type": "main", "index": 0}]]}, "Process Each Section": {"main": [[{"node": "Generate Images", "type": "main", "index": 0}, {"node": "Generate Voice", "type": "main", "index": 0}], [{"node": "Collect All Sections", "type": "main", "index": 0}]]}, "Generate Images": {"main": [[{"node": "Combine Section Assets", "type": "main", "index": 0}]]}, "Generate Voice": {"main": [[{"node": "Combine Section Assets", "type": "main", "index": 0}]]}, "Combine Section Assets": {"main": [[{"node": "Process Each Section", "type": "main", "index": 0}]]}, "Collect All Sections": {"main": [[{"node": "Render Final Video", "type": "main", "index": 0}]]}, "Render Final Video": {"main": [[{"node": "Wait for Video Processing", "type": "main", "index": 0}]]}, "Wait for Video Processing": {"main": [[{"node": "Add Subtitles", "type": "main", "index": 0}]]}, "Add Subtitles": {"main": [[{"node": "Prepare YouTube Upload", "type": "main", "index": 0}]]}, "Prepare YouTube Upload": {"main": [[{"node": "Upload to YouTube", "type": "main", "index": 0}]]}, "Upload to YouTube": {"main": [[{"node": "Telegram Success Notification", "type": "main", "index": 0}]]}, "Telegram Success Notification": {"main": [[{"node": "Final Output", "type": "main", "index": 0}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": {"node:Schedule Trigger": {"recurrenceRules": []}}, "meta": {"templateCredsSetupCompleted": true}, "pinData": null, "tags": [{"id": "youtube-automation", "name": "YouTube Automation"}, {"id": "mlx-video", "name": "MLX Video"}, {"id": "telegram-notifications", "name": "Telegram Notifications"}]}